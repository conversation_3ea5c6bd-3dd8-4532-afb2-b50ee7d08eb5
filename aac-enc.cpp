/* ------------------------------------------------------------------
 * Copyright (C) 2011 <PERSON>
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *	  http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either
 * express or implied.
 * See the License for the specific language governing permissions
 * and limitations under the License.
 * -------------------------------------------------------------------
 */

#include <stdio.h>
#include <stdint.h>
#include <string.h>
#include "time.h"
#include <errno.h>
#include <chrono>
#include <cmath>
#include <vector>
#include <numeric>
#include <iostream>

#if defined(_MSC_VER)
#include <getopt.h>
#else
#include <unistd.h>
#endif

#include <stdlib.h>
#include <string.h>
#include "libAACenc/include/aacenc_lib.h"
#include "libAACdec/include/aacdecoder_lib.h"
#include "wavreader.h"
#include "wavwriter.h"

#ifdef FDKAAC_DEMO_IOS
#include "device_helpers/ios/helpers.h"
#endif

#if defined(_WIN32) || defined( _WIN64)
#define POPEN(x, y) _popen(x, y)
   #define PCLOSE(x) _pclose(x)
#elif defined(__APPLE__)
#define POPEN(x, y)  popen(x, y)
#define PCLOSE(x) pclose(x)
#endif
#define STATS_FILE "stats_aac.txt"
#define STATS_DEC_FILE "stats_aac_dec.txt"
// #define DUMP_BITRATE
// #define DUMP_MUSIC_PROB

using namespace std;


struct DecoderType {
    int32_t sample_rate;
    int32_t channels_num;
    int32_t aac_aot;
};

typedef struct {
    ADTSInfo adts_info;
    double framesize_ms;
    int sbr_enabled;
    int ps_enabled;
    int sample_rate;
    int channel_num;
} AACStreamInfo;

typedef struct {
    int format;
    int sample_rate;
    int channels; 
    int bits_per_sample;
    int skipDelaySamples;
    int aot;
    double framesize_ms;
    TRANSPORT_TYPE transtype;
    double average_bitrate; 
    double maximum_bitrate;
    double minimum_bitrate;
    double bitrate_std;
    int vbr;
    int bitrate;
    double average_dec_rtf_ns;
    double average_enc_rtf_ns;

    float silence_ratio;
    float noise_ratio;
    float speech_ratio;
    float music_ratio;
#ifdef STEREO_DETECT_OPT
    float fake_stereo_ratio;
    float inverse_stereo_ratio;
    double average_spec_diff;
#endif
} EncStats;

static const int SamplingRateTable[] = {
        96000, 88200, 64000, 48000, 44100, 32000, 24000, 22050, 16000, 12000, 11025, 8000, 7350, 0, 0, 57600,
        51200, 40000, 38400, 34150, 28800, 25600, 20000, 19200, 17075, 14400, 12800, 9600, 0,    0, 0, 0};

static const int ChannelTable[] = {0, 1, 2, 3, 4, 5, 6, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0};

TRANSPORT_TYPE get_transtype(int index) {
    TRANSPORT_TYPE trans_type = TT_MP4_RAW;
    if(index == 1) {
        trans_type = TT_MP4_ADTS;
    } else {
        trans_type = TT_MP4_RAW;
    }
    return trans_type;
}

int getSamplingRateIndex(int sample_rate) {
    int sampleRateIndex = 0;
    int sampleRateTableSize = sizeof(SamplingRateTable) / sizeof(SamplingRateTable[0]);
    for (; sampleRateIndex < sampleRateTableSize && sampleRateIndex < 0xf; sampleRateIndex++) {
        if (sample_rate == SamplingRateTable[sampleRateIndex]) {
            break;
        }
    }

    return sampleRateIndex;
}

int getChannelIndex(int channel) {
    int channelIndex = 0;
    int channelTableSize = sizeof(ChannelTable) / sizeof(ChannelTable[0]);
    for (; channelIndex < channelTableSize; channelIndex++) {
        if (channel == ChannelTable[channelIndex]) {
            break;
        }
    }
    return channelIndex;
}

int rx_audio_gen_aac_asc(struct DecoderType dec_type, uint8_t* asc, int32_t len) {
    uint8_t aot, sr_idx, half_sr_idx, ch_mode;
    int invalid_len = 0;
    if (!asc) {
        return 0;
    }

    memset(asc, 0x00, len);

    if (20 == dec_type.aac_aot && dec_type.channels_num < 2) {
        return 0;
    }

    aot = dec_type.aac_aot;

    if (44100 == dec_type.sample_rate || 48000 == dec_type.sample_rate || 32000 == dec_type.sample_rate || 16000 == dec_type.sample_rate) {
        sr_idx = getSamplingRateIndex(dec_type.sample_rate);
        half_sr_idx = getSamplingRateIndex(dec_type.sample_rate>>1);
    } else {
        return 0;
    }

    ch_mode = getChannelIndex(dec_type.channels_num);

    if (2 == dec_type.aac_aot) {
        asc[0] = (aot << 3) | (sr_idx >> 1);
        asc[1] = (sr_idx << 7) | (ch_mode << 3);
        invalid_len = 2;
    } else if (5 == dec_type.aac_aot) {
        asc[0] = (aot << 3) | (half_sr_idx >> 1);
        asc[1] = (half_sr_idx << 7) | (ch_mode << 3) | (sr_idx >> 1);
        asc[2] = (sr_idx << 7) | (2 << 2);
        asc[3] = 0;
        invalid_len = 4;
    } else if (29 == dec_type.aac_aot) {
        asc[0] = (aot << 3) | (half_sr_idx >> 1);
        asc[1] = (half_sr_idx << 7) | (1 << 3) | (sr_idx >> 1);
        asc[2] = (sr_idx << 7) | (2 << 2);
        asc[3] = 0;
        invalid_len = 4;
    }

    return invalid_len;
}

static int64_t current_system_clock() {
    // count nano-seconds since system begin
    auto now_time = std::chrono::steady_clock::now();
    int64_t nano_time = std::chrono::duration_cast<std::chrono::nanoseconds>(now_time.time_since_epoch()).count();
    return nano_time;
}

static AAC_DECODER_ERROR get_AAC_stream_info(FILE *fbitstream, AACStreamInfo *aac_stream_info) {
    memset(aac_stream_info, 0, sizeof(AACStreamInfo));
    unsigned char *fdk_aac_input_buffer;
    fdk_aac_input_buffer = (unsigned char*)malloc(65536);
    ADTSInfo adts_info;
    int adts_frame_length = getAACFrame(fbitstream, fdk_aac_input_buffer, 65536, &adts_info);
    fseek(fbitstream, 0, SEEK_SET);
    
    int length = 4;
    uint8_t* asc = (uint8_t*)malloc(length);
    struct DecoderType dec_type;
    dec_type.aac_aot = adts_info.profile_aot;
    dec_type.sample_rate = adts_info.sample_rate;
    dec_type.channels_num = adts_info.channels;
    AAC_DECODER_ERROR err = AAC_DEC_OK;

    HANDLE_AACDECODER fdkaac_dec_handle_ = NULL;
    fdkaac_dec_handle_ = AudioX_aacDecoder_Open(get_transtype(adts_info.is_adts_format), 1);
    if ((err = AudioX_aacDecoder_SetParam(fdkaac_dec_handle_, AAC_PCM_MAX_OUTPUT_CHANNELS, dec_type.channels_num)) != AAC_DEC_OK) {
        printf("Unable to set fdkaac maxoutput channel.\n");
    }

    // set plc mode
    if ((err = AudioX_aacDecoder_SetParam(fdkaac_dec_handle_, AAC_CONCEAL_METHOD, 2)) != AAC_DEC_OK) {
        printf("Unable to set fdkaac concealment method.\n");
    }

    unsigned int invalid_len = rx_audio_gen_aac_asc(dec_type, asc, length);
    if (invalid_len > 0) {
        err = AudioX_aacDecoder_ConfigRaw(fdkaac_dec_handle_, &asc, &invalid_len);
        if (AAC_DEC_OK != err) {
            AudioX_aacDecoder_Close(fdkaac_dec_handle_);
            fdkaac_dec_handle_ = NULL;
            printf("aacDecoder_ConfigRaw failed, error code: %d.\n", err);
        }
    }
    if (asc != NULL)
        free(asc);
    
    CStreamInfo* decode_info;
    uint32_t encoded_len = adts_frame_length;
    uint32_t bytes_valid = encoded_len;

    err = AudioX_aacDecoder_Fill(fdkaac_dec_handle_, &fdk_aac_input_buffer, &encoded_len, &bytes_valid);
    if (AAC_DEC_OK != err) {
        AudioX_aacDecoder_Close(fdkaac_dec_handle_);
        fdkaac_dec_handle_ = NULL;
        printf("aacDecoder filldata failed, error code: %d.\n", err);
    }
    
    short *outdata=NULL;
    outdata = (short*)malloc(65536 * sizeof(short));
    err = AudioX_aacDecoder_DecodeFrame(fdkaac_dec_handle_, outdata, 65536, 0);
    decode_info = AudioX_aacDecoder_GetStreamInfo(fdkaac_dec_handle_);

    aac_stream_info->sample_rate = decode_info->sampleRate;
    aac_stream_info->channel_num = decode_info->numChannels;
    aac_stream_info->framesize_ms = (double)1000.0*decode_info->frameSize/aac_stream_info->sample_rate;
//    aac_stream_info->ps_enabled = decode_info->psEnabled;
//    aac_stream_info->sbr_enabled = decode_info->sbrEnabled;
    aac_stream_info->adts_info = adts_info;
    if(aac_stream_info->ps_enabled) {
        aac_stream_info->channel_num = 2;
    }
    free(fdk_aac_input_buffer);
    free(outdata);
    AudioX_aacDecoder_Close(fdkaac_dec_handle_);
    return err;
}

static const char* aot_to_string(int aot) {
  const char* aot_string = "unknown";
  switch (aot) {
    case AOT_AAC_LC:
      aot_string = "AAC-LC";
      break;
    case AOT_SBR:
      aot_string = "HE-AAC";
      break;
    case AOT_PS:
      aot_string = "HE-AACv2";
      break;
    default:
      break;
  }
  return aot_string;
}

static const char* transtype_to_string(int transtype) {
  const char* trans_type = "unknown";
  switch (transtype) {
    case TT_MP4_RAW:
      trans_type = "RAW";
      break;
    case TT_MP4_ADIF:
      trans_type = "ADIF";
      break;
    case TT_MP4_ADTS:
      trans_type = "ADTS";
      break;
    case TT_MP4_LATM_MCP1:
      trans_type = "MCP1";
      break;;
    case TT_MP4_LATM_MCP0:
      trans_type = "MCP0";
      break;
    case TT_MP4_LOAS:
      trans_type = "LOAS";
      break;
    case TT_DRM:
      trans_type = "DRM";
      break;
    default:
      break;
  }
  return trans_type;
}

static int process_encode(const char* infile, int aot, int eld_sbr, int vbr, int bitrate, TRANSPORT_TYPE transtype, int afterburner,
#ifdef BITRATE_STRATEGY_OPT
                          int strategy_mode,
#endif
#ifdef ABR_OPT
                          int abr_mode,
                          int low_cplx_opt,
#endif
                          const char* bitstreamfile, EncStats* out_enc_stats) {
    void *wav;
    int format;
    int sample_rate = 44100, channels = 1, bits_per_sample = 16;
    double framesize_ms = 0.0;
    int input_size;
    int skipDelaySamples = 0;
    uint8_t* input_buf;
    int16_t* convert_buf;
    int count = 0;
    double enc_total_rtf_ns = 0.0;
    double enc_max_rtf = 0.0;
    double average_enc_rtf_ns;
    FILE *fbitstream = NULL;

    double tot_samples = 0.0;
    double total_bits = 0.0;
    double bits2 = 0.0;
    double bits_max = 0.0;
    double bits_min = 65536.0;
    double bits_var = 0.0;
    double average_bitrate = 0.0, maximum_bitrate = 0.0, minimum_bitrate = 0.0, bitrate_std = 0.0;
    long long analysis_count = 0, silence_count = 0, noise_count = 0, speech_count = 0, music_count = 0;
    double speech_ratio = 0.0, music_ratio = 0.0, noise_ratio = 0.0, silence_ratio = 0.0;

#ifdef STEREO_DETECT_OPT
    long long fake_stereo_count = 0, inverse_stereo_count = 0;
    double fake_stereo_ratio = 0.0, inverse_stereo_ratio = 0.0;
    double average_spec_diff = 0.0;
    vector<double> spec_diff_vec;
#endif

    printf("---- Start Encoding. ----\n");

    HANDLE_AACENCODER handle;
    CHANNEL_MODE mode;
    AACENC_InfoStruct info = { 0 };
    wav = wav_read_open(infile);
    if (!wav) {
        fprintf(stderr, "Unable to open wav file %s\n", infile);
        return 1;
    }

    if (!wav_get_header(wav, &format, &channels, &sample_rate, &bits_per_sample, NULL)) {
        fprintf(stderr, "Bad wav file %s\n", infile);
        return 1;
    }
    if (format != 1) {
        fprintf(stderr, "Unsupported WAV format %d\n", format);
        return 1;
    }
    if (bits_per_sample != 16) {
        fprintf(stderr, "Unsupported WAV sample depth %d\n", bits_per_sample);
        return 1;
    }

    if(aot == 2) {  //AAC LC
        if (sample_rate == 48000) {
          skipDelaySamples = 3791 * channels;
        } else if (sample_rate == 44100) {
          skipDelaySamples = 3733 * channels;
        }
    } else if (aot == 5) {  //HE-AAC
        if (sample_rate == 48000) {
          skipDelaySamples = 7823 * channels;
        } else if (sample_rate == 44100) {
          skipDelaySamples = 7766 * channels;
        }
    } else if (aot == 29) { //HE-AAC v2
        if (sample_rate == 48000) {
          skipDelaySamples = 9871 * channels;
        } else if (sample_rate == 44100) {
          skipDelaySamples = 9814 * channels;
        }
    }

    switch (channels) {
        case 1: mode = MODE_1;       break;
        case 2: mode = MODE_2;       break;
        case 3: mode = MODE_1_2;     break;
        case 4: mode = MODE_1_2_1;   break;
        case 5: mode = MODE_1_2_2;   break;
        case 6: mode = MODE_1_2_2_1; break;
        default:
            fprintf(stderr, "Unsupported WAV channels %d\n", channels);
            return 1;
    }
    if (AudioX_aacEncOpen(&handle, 0, channels) != AACENC_OK) {
        fprintf(stderr, "Unable to open encoder\n");
        return 1;
    }
    if (AudioX_aacEncoder_SetParam(handle, AACENC_AOT, aot) != AACENC_OK) {
        fprintf(stderr, "Unable to set the AOT\n");
        return 1;
    }
    if (aot == 39 && eld_sbr) {
        if (AudioX_aacEncoder_SetParam(handle, AACENC_SBR_MODE, 1) != AACENC_OK) {
            fprintf(stderr, "Unable to set SBR mode for ELD\n");
            return 1;
        }
    }
    if (AudioX_aacEncoder_SetParam(handle, AACENC_SAMPLERATE, sample_rate) != AACENC_OK) {
        fprintf(stderr, "Unable to set the AOT\n");
        return 1;
    }
    if (AudioX_aacEncoder_SetParam(handle, AACENC_CHANNELMODE, mode) != AACENC_OK) {
        fprintf(stderr, "Unable to set the channel mode\n");
        return 1;
    }
    if (AudioX_aacEncoder_SetParam(handle, AACENC_CHANNELORDER, 1) != AACENC_OK) {
        fprintf(stderr, "Unable to set the wav channel order\n");
        return 1;
    }
#ifdef ABR_OPT
    if (abr_mode > 0) {
        if (AudioX_aacEncoder_SetParam(handle, AACENC_ABR_OPEN_LOOP,
                                   abr_mode) != AACENC_OK) {
            fprintf(stderr, "Unable to set the abr bitrate\n");
            return 1;
        }
    }
//    if (AudioX_aacEncoder_SetParam(handle, AACENC_ABR_COMPLEX,
//        abr_cplx) != AACENC_OK) {
//        fprintf(stderr, "Unable to set the complex mode\n");
//        return 1;
//    }
#endif
    if (vbr) {
        if (AudioX_aacEncoder_SetParam(handle, AACENC_BITRATEMODE, vbr) != AACENC_OK) {
            fprintf(stderr, "Unable to set the VBR bitrate mode\n");
            return 1;
        }

    } else {
        if (AudioX_aacEncoder_SetParam(handle, AACENC_BITRATE, bitrate) != AACENC_OK) {
            fprintf(stderr, "Unable to set the bitrate\n");
            return 1;
        }
#ifdef BITRATE_STRATEGY_OPT
        if (AudioX_aacEncoder_SetParam(handle, AACENC_BITRATE_STRATEGY_MODE, strategy_mode) != AACENC_OK) {
          fprintf(stderr, "Unable to set the bitrate strategy mode\n");
          return 1;
        }
        if (strategy_mode>0 && strategy_mode<4 && mode==2 &&bitrate>40000){
          if (AudioX_aacEncoder_SetParam(handle, AACENC_ENC_COMPLEX,
                                          low_cplx_opt) != AACENC_OK) {
                  fprintf(stderr, "Unable to set the complex mode\n");
                  return 1;
          }
        }
#endif
    }
    if (AudioX_aacEncoder_SetParam(handle, AACENC_TRANSMUX, transtype) != AACENC_OK) {
        fprintf(stderr, "Unable to set the ADTS transmux\n");
        return 1;
    }
    if (AudioX_aacEncoder_SetParam(handle, AACENC_AFTERBURNER, afterburner) != AACENC_OK) {
        fprintf(stderr, "Unable to set the afterburner mode\n");
        return 1;
    }
    if (AudioX_aacEncEncode(handle, NULL, NULL, NULL, NULL) != AACENC_OK) {
        fprintf(stderr, "Unable to initialize the encoder\n");
        return 1;
    }
    if (AudioX_aacEncInfo(handle, &info) != AACENC_OK) {
        fprintf(stderr, "Unable to get the encoder info\n");
        return 1;
    }

    fbitstream = fopen(bitstreamfile, "wb");
    if (!fbitstream) {
        perror(bitstreamfile);
        return 1;
    }

    framesize_ms = (double)1000.0*info.frameLength/sample_rate;
    input_size = channels*2*info.frameLength;
    input_buf = (uint8_t*) malloc(input_size);
    convert_buf = (int16_t*) malloc(input_size);

#ifdef DUMP_MUSIC_PROB
    void *f_music_prob = NULL;
    void *f_activity_prob = NULL;
    void *f_is_silence = NULL;
    void *f_valid_bandwidth = NULL;
    void *f_spec_flatness = NULL;
    void *f_out_bitrate = NULL;
    // void *f_set_bitrate = NULL;
    // void *f_bitrate_error = NULL;
    void *f_signal_type = NULL;

    char infile_path[1000];
    strcpy(infile_path, infile);
    char *split_str = strtok(infile_path, "/");
    char *infile_str = NULL;
    while (split_str != NULL) {
      infile_str = split_str;
      split_str = strtok(NULL, "/");
    }
    infile_str = strtok(infile_str, ".");
#endif

    while (1) {
        AACENC_BufDesc in_buf = { 0 }, out_buf = { 0 };
        AACENC_InArgs in_args = { 0 };
        AACENC_OutArgs out_args = { 0 };
        int in_identifier = IN_AUDIO_DATA;
        int in_size, in_elem_size;
        int out_identifier = OUT_BITSTREAM_DATA;
        int out_size, out_elem_size;
        int read, i;
        void *in_ptr, *out_ptr;
        uint8_t outbuf[20480];
        AACENC_ERROR err;

        read = wav_read_data(wav, input_buf, input_size);
        for (i = 0; i < read/2; i++) {
            const uint8_t* in = &input_buf[2*i];
            convert_buf[i] = in[0] | (in[1] << 8);
        }
        if (read <= 0) {
            in_args.numInSamples = -1;
        } else {
            in_ptr = convert_buf;
            in_size = read;
            in_elem_size = 2;

            in_args.numInSamples = read/2;
            in_buf.numBufs = 1;
            in_buf.bufs = &in_ptr;
            in_buf.bufferIdentifiers = &in_identifier;
            in_buf.bufSizes = &in_size;
            in_buf.bufElSizes = &in_elem_size;
        }
        out_ptr = outbuf;
        out_size = sizeof(outbuf);
        out_elem_size = 1;
        out_buf.numBufs = 1;
        out_buf.bufs = &out_ptr;
        out_buf.bufferIdentifiers = &out_identifier;
        out_buf.bufSizes = &out_size;
        out_buf.bufElSizes = &out_elem_size;

       int64_t start_time = current_system_clock();

        if ((err = AudioX_aacEncEncode(handle, &in_buf, &out_buf, &in_args, &out_args)) != AACENC_OK) {
            if (err == AACENC_ENCODE_EOF)
                break;
            fprintf(stderr, "Encoding failed\n");
            return 1;
        }
#ifdef DUMP_MUSIC_PROB
      if (f_music_prob == NULL) {
            char tmp_str[1000];
            strcpy(tmp_str, infile_str);
            strcat(tmp_str, "_music_prob.wav");
            f_music_prob = wav_write_open(tmp_str, sample_rate,
                                          bits_per_sample, 2);
          }
          if (f_activity_prob == NULL) {
            char tmp_str[1000];
            strcpy(tmp_str, infile_str);
            strcat(tmp_str, "_activity_prob.wav");
            f_activity_prob = wav_write_open(tmp_str, sample_rate,
                                             bits_per_sample, 2);
          }
          if (f_is_silence == NULL) {
            char tmp_str[1000];
            strcpy(tmp_str, infile_str);
            strcat(tmp_str, "_is_silence.wav");
            f_is_silence = wav_write_open(tmp_str, sample_rate,
                                          bits_per_sample, 2);
          }

          if (f_valid_bandwidth == NULL) {
            char tmp_str[1000];
            strcpy(tmp_str, infile_str);
            strcat(tmp_str, "_valid_bandwidth.wav");
            f_valid_bandwidth = wav_write_open(tmp_str, sample_rate,
                                          bits_per_sample, 2);
          }

          if (f_spec_flatness == NULL) {
            char tmp_str[1000];
            strcpy(tmp_str, infile_str);
            strcat(tmp_str, "_spec_flatness.wav");
            f_spec_flatness = wav_write_open(tmp_str, sample_rate,
                                          bits_per_sample, 2);
          }

          if (f_out_bitrate == NULL) {
            char tmp_str[1000];
            strcpy(tmp_str, infile_str);
            strcat(tmp_str, "_out_bitrate.wav");
            f_out_bitrate = wav_write_open(tmp_str, sample_rate,
                                          bits_per_sample, 2);
          }

        //   if (f_set_bitrate == NULL) {
        //     char tmp_str[1000];
        //     strcpy(tmp_str, infile_str);
        //     strcat(tmp_str, "_set_bitrate.wav");
        //     f_set_bitrate = wav_write_open(tmp_str, sample_rate,
        //                                   bits_per_sample, 2);
        //   }

        //   if (f_bitrate_error == NULL) {
        //     char tmp_str[1000];
        //     strcpy(tmp_str, infile_str);
        //     strcat(tmp_str, "_bitrate_error.wav");
        //     f_bitrate_error = wav_write_open(tmp_str, sample_rate,
        //                                   bits_per_sample, 2);
        //   }

          if (f_signal_type == NULL) {
            char tmp_str[1000];
            strcpy(tmp_str, infile_str);
            strcat(tmp_str, "_signal_type.wav");
            f_signal_type = wav_write_open(tmp_str, sample_rate,
                                          bits_per_sample, 2);
          }

          short tmp_data[20480];
          unsigned char tmp_bytes[20480*2];
          AnalysisInfo analysis_info = aac_get_analysis_info(handle);

          for (i = 0; i < in_args.numInSamples / channels; i ++) {
            tmp_data[2*i] = convert_buf[i*channels];
            tmp_data[2*i+1] = (opus_int16)(analysis_info.music_prob * 10000);
          }
          for (i = 0; i < in_args.numInSamples / channels*2; i ++) {
            short s;
            s = tmp_data[i];
            tmp_bytes[2*i] = s&0xFF;
            tmp_bytes[2*i+1] = (s>>8)&0xFF;
          }
          if (read > 0)
            wav_write_data(f_music_prob, tmp_bytes, sizeof(short)*read/2/channels*2);

          for (i = 0; i < in_args.numInSamples / channels; i ++) {
            tmp_data[2*i+1] = (opus_int16)((analysis_info.activity_probability<0.2 ? 0:1) * 10000);
          }
          for (i = 0; i < in_args.numInSamples / channels*2; i ++) {
            short s;
            s = tmp_data[i];
            tmp_bytes[2*i] = s&0xFF;
            tmp_bytes[2*i+1] = (s>>8)&0xFF;
          }
          if (read > 0)
            wav_write_data(f_activity_prob, tmp_bytes, sizeof(short)*read/2/channels*2);

          for (i = 0; i < in_args.numInSamples / channels; i ++) {
            tmp_data[2*i+1] = (opus_int16)(analysis_info.analysis_validbandwidth);
          }
          for (i = 0; i < in_args.numInSamples / channels*2; i ++) {
            short s;
            s = tmp_data[i];
            tmp_bytes[2*i] = s&0xFF;
            tmp_bytes[2*i+1] = (s>>8)&0xFF;
          }
          if (read > 0)
            wav_write_data(f_valid_bandwidth, tmp_bytes, sizeof(short)*read/2/channels*2);
        
         for (i = 0; i < in_args.numInSamples / channels; i ++) {
            tmp_data[2*i+1] = (opus_int16)(analysis_info.spec_flatness*30000);
          }
          for (i = 0; i < in_args.numInSamples / channels*2; i ++) {
            short s;
            s = tmp_data[i];
            tmp_bytes[2*i] = s&0xFF;
            tmp_bytes[2*i+1] = (s>>8)&0xFF;
          }
          if (read > 0)
            wav_write_data(f_spec_flatness, tmp_bytes, sizeof(short)*read/2/channels*2);

         for (i = 0; i < in_args.numInSamples / channels; i ++) {
            tmp_data[2*i+1] = (opus_int16)(out_args.numOutBytes * 8 * sample_rate / (in_args.numInSamples/channels) / 10);
          }
          for (i = 0; i < in_args.numInSamples / channels*2; i ++) {
            short s;
            s = tmp_data[i];
            tmp_bytes[2*i] = s&0xFF;
            tmp_bytes[2*i+1] = (s>>8)&0xFF;
          }
          if (read > 0)
            wav_write_data(f_out_bitrate, tmp_bytes, sizeof(short)*read/2/channels*2);
         
        //  for (i = 0; i < in_args.numInSamples / channels; i ++) {
        //     tmp_data[2*i+1] = (opus_int16)(out_args.setBitrate/10);
        //   }
        //   for (i = 0; i < in_args.numInSamples / channels*2; i ++) {
        //     short s;
        //     s = tmp_data[i];
        //     tmp_bytes[2*i] = s&0xFF;
        //     tmp_bytes[2*i+1] = (s>>8)&0xFF;
        //   }
        //   if (read > 0)
        //     wav_write_data(f_set_bitrate, tmp_bytes, sizeof(short)*read/2/channels*2);

        //   for (i = 0; i < in_args.numInSamples / channels; i ++) {
        //     tmp_data[2*i+1] = (opus_int16)((out_args.numOutBytes * 8 * sample_rate / (in_args.numInSamples/channels) / 10) - out_args.setBitrate/10);
        //   }
        //   for (i = 0; i < in_args.numInSamples / channels*2; i ++) {
        //     short s;
        //     s = tmp_data[i];
        //     tmp_bytes[2*i] = s&0xFF;
        //     tmp_bytes[2*i+1] = (s>>8)&0xFF;
        //   }
        //   if (read > 0)
        //     wav_write_data(f_bitrate_error, tmp_bytes, sizeof(short)*read/2/channels*2);
          
          for (i = 0; i < in_args.numInSamples / channels; i ++) {
            if (analysis_info.signal_type == BA_SILENCE) {
                tmp_data[2*i+1] = -15000;
            } else if (analysis_info.signal_type == BA_NOISE) {
                tmp_data[2*i+1] = -5000;
            } else if (analysis_info.signal_type == BA_SPEECH) {
                tmp_data[2*i+1] = 5000;
            } else if (analysis_info.signal_type == BA_MUSIC) {
                tmp_data[2*i+1] = 15000;
            } else {
                tmp_data[2*i+1] = 20000;
            }
          }
          for (i = 0; i < in_args.numInSamples / channels*2; i ++) {
            short s;
            s = tmp_data[i];
            tmp_bytes[2*i] = s&0xFF;
            tmp_bytes[2*i+1] = (s>>8)&0xFF;
          }
          if (read > 0)
            wav_write_data(f_signal_type, tmp_bytes, sizeof(short)*read/2/channels*2);

          for (i = 0; i < in_args.numInSamples / channels; i ++) {
            tmp_data[2*i+1] = (opus_int16)(analysis_info.is_silence * 10000);
          }
          for (i = 0; i < in_args.numInSamples / channels*2; i ++) {
            short s;
            s = tmp_data[i];
            tmp_bytes[2*i] = s&0xFF;
            tmp_bytes[2*i+1] = (s>>8)&0xFF;
          }
          if (read > 0)
            wav_write_data(f_is_silence, tmp_bytes, sizeof(short)*read/2/channels*2);
#endif
        count++;
        int64_t end_time = current_system_clock();
        int64_t enc_one_frame_time_ns = end_time - start_time;
        enc_total_rtf_ns += enc_one_frame_time_ns;
        if (enc_one_frame_time_ns > enc_max_rtf){
            enc_max_rtf = (double)enc_one_frame_time_ns;
        }

        if (out_args.numOutBytes == 0) {
            printf("delay %d samples.\n", in_args.numInSamples);
            continue;
        }
        if(transtype != TT_MP4_ADTS) {
            unsigned char int_field[4];
            int_to_char(out_args.numOutBytes, int_field);
            if (fwrite(int_field, 1, 4, fbitstream) != 4) {
                fprintf(stderr, "Error writing.\n");
            }
        }
#if (defined ABR_OPT) || (defined BITRATE_STRATEGY_OPT)
#ifdef STEREO_DETECT_OPT
        AnalysisInfo tmp_info = ba_aac_get_analysis_info(handle);
        if (tmp_info.signal_type == BA_SPEECH ||
            tmp_info.signal_type == BA_NOISE ||
            tmp_info.signal_type == BA_MUSIC) {
            spec_diff_vec.push_back(tmp_info.spec_diff);
        }
#endif
#endif
        // printf("out_args.numOutBytes:%d\n", out_args.numOutBytes);
        fwrite(outbuf, 1, out_args.numOutBytes, fbitstream);
        tot_samples += info.frameLength;
        total_bits += out_args.numOutBytes * 8;
        bits2 += out_args.numOutBytes * out_args.numOutBytes * 64;
        bits_max = (out_args.numOutBytes * 8 > bits_max)
                        ? out_args.numOutBytes * 8
                        : bits_max;
        bits_min = (out_args.numOutBytes * 8 < bits_min)
                        ? out_args.numOutBytes * 8
                        : bits_min;
    }
#ifdef DUMP_MUSIC_PROB
    if (f_music_prob)
      wav_write_close(f_music_prob);
    if (f_activity_prob)
      wav_write_close(f_activity_prob);
    if (f_is_silence)
      wav_write_close(f_is_silence);
    if (f_valid_bandwidth)
      wav_write_close(f_valid_bandwidth);
    if (f_spec_flatness)
      wav_write_close(f_spec_flatness);
    if(f_out_bitrate)
      wav_write_close(f_out_bitrate);
    // if(f_set_bitrate)
    //   wav_write_close(f_set_bitrate);
    // if(f_bitrate_error)
    //   wav_write_close(f_bitrate_error);
    if(f_signal_type)
      wav_write_close(f_signal_type);
#endif
    average_bitrate = 1e-3 * total_bits * sample_rate / tot_samples;
    maximum_bitrate = 1e-3 * bits_max * sample_rate / info.frameLength;
    minimum_bitrate = 1e-3 * bits_min * sample_rate / info.frameLength;
    bits_var =
        bits2 / count - total_bits * total_bits / (count * (double)count);
    bitrate_std = 1e-3 * sqrt(bits_var) * sample_rate / info.frameLength;
#ifdef BITRATE_STRATEGY_OPT
    analysis_count = ba_aac_get_analysis_frame_count(handle);
    silence_count = ba_aac_get_silence_frame_count(handle);
    noise_count = ba_aac_get_noise_frame_count(handle);
    speech_count = ba_aac_get_speech_frame_count(handle);
    music_count = ba_aac_get_music_frame_count(handle);
#ifdef STEREO_DETECT_OPT
    fake_stereo_count = ba_aac_get_fake_stereo_frame_count(handle);
    inverse_stereo_count = ba_aac_get_inverse_stereo_frame_count(handle);
#endif
#endif

    speech_ratio = (double)speech_count/analysis_count;
    music_ratio = (double)music_count/analysis_count;
    noise_ratio = (double)noise_count/analysis_count;
    silence_ratio = (double)silence_count/analysis_count;
#ifdef STEREO_DETECT_OPT
    fake_stereo_ratio = (double)fake_stereo_count / analysis_count;
    inverse_stereo_ratio = (double)inverse_stereo_count / analysis_count;
    average_spec_diff = std::accumulate(spec_diff_vec.begin(), spec_diff_vec.end(), double(0.0));
    if (static_cast<int>(spec_diff_vec.size()) > 0) {
        average_spec_diff = average_spec_diff/spec_diff_vec.size();
    }
#endif

    printf("%d frames encoded.\n", count);
    free(input_buf);
    free(convert_buf);
    fclose(fbitstream);
    wav_read_close(wav);
    AudioX_aacEncClose(&handle);

    fprintf(stderr, "/***************analysis result***************/\n");
    fprintf(stderr, "speech_ratio:              %.2f\n", speech_ratio);
    fprintf(stderr, "music_ratio:               %.2f\n", music_ratio);
    fprintf(stderr, "noise_ratio:               %.2f\n", noise_ratio);
    fprintf(stderr, "silence_ratio:             %.2f\n", silence_ratio);
#ifdef STEREO_DETECT_OPT
    fprintf(stderr, "fake_stereo_ratio:         %.2f\n", fake_stereo_ratio);
    fprintf(stderr, "inverse_stereo_ratio:      %.2f\n", inverse_stereo_ratio);
    fprintf(stderr, "average_spec_diff:         %.4f\n", average_spec_diff);
#endif
    fprintf(stderr, "/*********************************************/\n");

    out_enc_stats->speech_ratio = (float)speech_ratio;
    out_enc_stats->music_ratio = (float)music_ratio;
    out_enc_stats->noise_ratio = (float)noise_ratio;
    out_enc_stats->silence_ratio = (float)silence_ratio;
#ifdef STEREO_DETECT_OPT
    out_enc_stats->fake_stereo_ratio = (float)fake_stereo_ratio;
    out_enc_stats->inverse_stereo_ratio = (float)inverse_stereo_ratio;
    out_enc_stats->average_spec_diff = average_spec_diff;
#endif

    average_enc_rtf_ns = (enc_total_rtf_ns/(1000000*framesize_ms)) / count;
    fprintf (stderr, "average enc rtf(ms/ms):             %7.5f\n",average_enc_rtf_ns);
    double max_enc_rtf = (enc_max_rtf/(1000000*framesize_ms)) ;
    fprintf (stderr, "max enc rtf(ms/ms):                 %7.5f\n",max_enc_rtf);

    out_enc_stats->bits_per_sample = bits_per_sample;
    out_enc_stats->channels = channels;
    out_enc_stats->format = format;
    out_enc_stats->sample_rate = sample_rate;
    out_enc_stats->skipDelaySamples = skipDelaySamples;
    out_enc_stats->framesize_ms = framesize_ms;
    out_enc_stats->aot = aot;
    out_enc_stats->transtype = transtype;

    fprintf(stderr, "/***********enc bitrate information***********/\n");
    fprintf(stderr, "average bitrate:             %7.3f kb/s\n", average_bitrate);
    fprintf(stderr, "maximum bitrate:             %7.3f kb/s\n", maximum_bitrate);
    fprintf(stderr, "minimum bitrate:             %7.3f kb/s\n", minimum_bitrate);
    fprintf(stderr, "bitrate standard deviation:  %7.3f kb/s\n", bitrate_std);

    out_enc_stats->average_bitrate = average_bitrate;
    out_enc_stats->maximum_bitrate = maximum_bitrate;
    out_enc_stats->minimum_bitrate = minimum_bitrate;
    out_enc_stats->bitrate_std = bitrate_std;

    out_enc_stats->bitrate = bitrate;
    out_enc_stats->vbr = vbr;
    out_enc_stats->average_enc_rtf_ns = average_enc_rtf_ns;
    out_enc_stats->average_dec_rtf_ns = 0.0000f;
    
    return 0;
}

static int process_decode(const char* bitstreamfile, int decode_only, const char* outfile, 
                        const EncStats *input_enc_stats, EncStats *output_dec_stats) {
    int sample_rate = 44100, channels = 1, bits_per_sample = 16;
    double framesize_ms = 0.0;
    int skipDelaySamples = 0, skippedSamples = 0;
    double dec_total_rtf_ns = 0.0;
    double dec_max_rtf = 0.0;
    double average_dec_rtf_ns;
    int count = 0;
    TRANSPORT_TYPE transtype = TT_MP4_ADTS;
    int aot = 2;
    void *fout_wav;
#ifdef DUMP_BITRATE
    void *fbit_wav = NULL;
#endif
    FILE *fbitstream = NULL;

    double tot_samples = 0.0;
    double total_bits = 0.0;
    double bits2 = 0.0;
    double bits_max = 0.0;
    double bits_min = 65536.0;
    double bits_var = 0.0;
    double average_bitrate = 0.0, maximum_bitrate = 0.0, minimum_bitrate = 0.0, bitrate_std = 0.0;
    int need_output_dec_bitrate = 0;

    printf("---- Start decoding. ----\n");
    AAC_DECODER_ERROR err = AAC_DEC_OK;
    fbitstream = fopen(bitstreamfile, "rb");
    count = 0;
    // Decode part
    struct DecoderType dec_type;
    if(decode_only) {
        need_output_dec_bitrate = 1;
        AACStreamInfo aac_stream_info;
        err = get_AAC_stream_info(fbitstream, &aac_stream_info);
        if(err != AAC_DEC_OK) {
            fprintf(stderr, "get info error!\n");
            return 1;
        }
        dec_type.aac_aot = aac_stream_info.adts_info.profile_aot;
        dec_type.channels_num = aac_stream_info.channel_num;
        dec_type.sample_rate = aac_stream_info.sample_rate;
        transtype = get_transtype(aac_stream_info.adts_info.is_adts_format);
        if(transtype != TT_MP4_ADTS) {
            printf("decode_only mode only support adts format.\n");
            return 1;
        }
        sample_rate = aac_stream_info.sample_rate;
        channels = aac_stream_info.channel_num;
        framesize_ms = aac_stream_info.framesize_ms;
        if(aac_stream_info.ps_enabled == 1) {  //HE-AAC v2
            if (sample_rate == 48000) {
                skipDelaySamples = 9871 * channels;
            } else if (sample_rate == 44100) {
                skipDelaySamples = 9814 * channels;
            }
        } else if (aac_stream_info.sbr_enabled == 1) {  //HE-AAC
            if (sample_rate == 48000) {
                skipDelaySamples = 7823 * channels;
            } else if (sample_rate == 44100) {
                skipDelaySamples = 7766 * channels;
            }
        } else { //AAC LC
            if (sample_rate == 48000) {
                skipDelaySamples = 3791 * channels;
            } else if (sample_rate == 44100) {
                skipDelaySamples = 3733 * channels;
            }
        }
        if (need_output_dec_bitrate) {
            // Decode_only usage, we don't skip delay samples
            skipDelaySamples = 0;
        }
    } else {
        sample_rate = input_enc_stats->sample_rate;
        bits_per_sample = input_enc_stats->bits_per_sample;
        channels = input_enc_stats->channels;
        aot = input_enc_stats->aot;
        skipDelaySamples = input_enc_stats->skipDelaySamples;
        transtype = input_enc_stats->transtype;
        framesize_ms = input_enc_stats->framesize_ms;
        dec_type.aac_aot = aot;
        dec_type.sample_rate = sample_rate;
        dec_type.channels_num = channels;
    }
    
    fout_wav = wav_write_open(outfile, sample_rate, bits_per_sample, channels);
    if (!fout_wav) {
        fprintf(stderr, "Unable to open output wav file %s\n", outfile);
        return 1;
    }
#ifdef DUMP_BITRATE
    char outfile_path[1000];
    strcpy(outfile_path, outfile);
    char *split_str = strtok(outfile_path, "/");
    char *outfile_str = NULL;
    while (split_str != NULL) {
      outfile_str = split_str;
      split_str = strtok(NULL, "/");
    }
    outfile_str = strtok(outfile_str, ".");
    strcat(outfile_str, "_bitrate_file.wav");
    fbit_wav = wav_write_open(outfile_str, sample_rate, bits_per_sample, 2);
#endif

    HANDLE_AACDECODER fdkaac_dec_handle_ = NULL;
    fdkaac_dec_handle_ = AudioX_aacDecoder_Open(transtype, 1);
    if ((err = AudioX_aacDecoder_SetParam(fdkaac_dec_handle_, AAC_PCM_MAX_OUTPUT_CHANNELS, dec_type.channels_num)) != AAC_DEC_OK) {
        printf("Unable to set fdkaac maxoutput channel.\n");
    }

    // set plc mode
    if ((err = AudioX_aacDecoder_SetParam(fdkaac_dec_handle_, AAC_CONCEAL_METHOD, 2)) != AAC_DEC_OK) {
        printf("Unable to set fdkaac concealment method.\n");
    }
    
    if (transtype == TT_MP4_RAW) {
        // AAC transtype is raw data.
        int length = 4;
        uint8_t* asc = (uint8_t*)malloc(length);
        unsigned int invalid_len = rx_audio_gen_aac_asc(dec_type, asc, length);
        if (invalid_len > 0) {
            err = AudioX_aacDecoder_ConfigRaw(fdkaac_dec_handle_, &asc, &invalid_len);
            if (AAC_DEC_OK != err) {
                AudioX_aacDecoder_Close(fdkaac_dec_handle_);
                fdkaac_dec_handle_ = NULL;
                printf("aacDecoder_ConfigRaw failed, error code: %d.\n", err);
                return 1;
            }
        }
        if (asc != NULL)
            free(asc);
    }
    
    if (AudioX_aacDecoder_SetParam(fdkaac_dec_handle_, AAC_PCM_MAX_OUTPUT_CHANNELS, channels) != AAC_DEC_OK) {
        printf("Unable to set output channels in the decoder\n");
    }

    CStreamInfo* decode_info;
    int need_flush_times = 1;
    if (dec_type.aac_aot == 2) {
        // AAC_LC need flush twice
        need_flush_times = 2;
    }

    unsigned char *fdk_aac_input_buffer;
    fdk_aac_input_buffer = (unsigned char*)malloc(65536);

    uint32_t encoded_len;
    int adts_frame_length;
    short *outdata=NULL;
    outdata = (short*)malloc(65536 * sizeof(short));
    unsigned char *fbytes=NULL;
    fbytes = (unsigned char*)malloc(65536);
#ifdef DUMP_BITRATE
    short bitrate_data[20480];
    unsigned char bitrate_bytes[20480*2];
#endif
    while (1) {
        ADTSInfo adts_info;
        adts_frame_length = getAACFrame(fbitstream, fdk_aac_input_buffer, 65536, &adts_info);
        encoded_len = adts_frame_length;
        uint32_t bytes_valid = encoded_len;
        if (adts_frame_length <= 0 && need_flush_times > 0) {
            printf("flush buffer.\n");
            if ((err = AudioX_aacDecoder_SetParam(fdkaac_dec_handle_, AAC_TPDEC_CLEAR_BUFFER, 1)) != AAC_DEC_OK) {
                printf("failed to clear buffer when flushing\n");
            }
            need_flush_times--;
        }

        err = AudioX_aacDecoder_Fill(fdkaac_dec_handle_, &fdk_aac_input_buffer, &encoded_len, &bytes_valid);
                if (AAC_DEC_OK != err) {         
            // aacDecoder_Close(fdkaac_dec_handle_);
            fdkaac_dec_handle_ = NULL;
            printf("aacDecoder filldata failed, error code: %d.\n", err);
            break;
        }


        int64_t start_time = current_system_clock();
        err = AudioX_aacDecoder_DecodeFrame(fdkaac_dec_handle_, outdata, 65536, 0);
        int64_t end_time = current_system_clock();
        int64_t dec_one_frame_time_ns = end_time - start_time;
        dec_total_rtf_ns += dec_one_frame_time_ns;
        if (dec_one_frame_time_ns > dec_max_rtf){
            dec_max_rtf = (double)dec_one_frame_time_ns;
        }
        if (AAC_DEC_TRANSPORT_SYNC_ERROR == err || AAC_DEC_NOT_ENOUGH_BITS == err) {
            printf("count = %d, Decoder_Frame err = %d.\n", count, err);
            printf("aacDecoder DecodeFrame end.\n", err);
            break;
        }
        if (AAC_DEC_OK != err) {
            AudioX_aacDecoder_Close(fdkaac_dec_handle_);
            fdkaac_dec_handle_ = NULL;
            printf("aacDecoder DecodeFrame failed, error code: %d.\n", err);
            return 1;
        }

        int i;
        decode_info = AudioX_aacDecoder_GetStreamInfo(fdkaac_dec_handle_);
        int decode_samples = decode_info->frameSize;
        
        if (adts_frame_length > 0) {
            count++;
            tot_samples += decode_samples;
            total_bits += encoded_len * 8;
            bits2 += encoded_len * encoded_len * 64;
            bits_max = (encoded_len * 8 > bits_max) ? encoded_len * 8 : bits_max;
            bits_min = (encoded_len * 8 < bits_min) ? encoded_len * 8 : bits_min;

            // write data to out file
#ifdef DUMP_BITRATE
            for (i = 0; i < decode_samples; i++) {
                bitrate_data[2 * i] = outdata[i * channels];
                bitrate_data[2 * i + 1] =
                    encoded_len * 8 * sample_rate / decode_samples / 10;
            }
            for (i = 0; i < decode_samples*2; i ++) {
                short s;
                s = bitrate_data[i];
                bitrate_bytes[2*i] = s&0xFF;
                bitrate_bytes[2*i+1] = (s>>8)&0xFF;
            }
#endif
        }

        for(i=0;i<decode_samples*channels;i++)
        {
            short s;
            s=outdata[i];
            fbytes[2*i]=s&0xFF;
            fbytes[2*i+1]=(s>>8)&0xFF;
        }
        // skip delay samples.
        if(skippedSamples < skipDelaySamples) {
            skippedSamples += decode_samples*channels;
            if(skippedSamples <= skipDelaySamples) {
                //all samples should be skipped for the current frame.
            } else {
                int availbleSamples = skippedSamples - skipDelaySamples;
                wav_write_data(fout_wav,&fbytes[2*(decode_samples*channels - availbleSamples)], sizeof(short)*availbleSamples );
#ifdef DUMP_BITRATE
                availbleSamples = channels == 1 ? availbleSamples * 2 : availbleSamples;
                wav_write_data(fbit_wav, &bitrate_bytes[2*(decode_samples*2 - availbleSamples)], sizeof(short)*availbleSamples);
#endif
            }
        } else
        {
            wav_write_data(fout_wav,fbytes, sizeof(short)*channels*decode_samples );
#ifdef DUMP_BITRATE
            wav_write_data(fbit_wav, bitrate_bytes, sizeof(short)*decode_samples*2);
#endif
        }
    }

    average_bitrate = 1e-3*total_bits*sample_rate/tot_samples;
    maximum_bitrate = 1e-3*bits_max*sample_rate/decode_info->frameSize;
    minimum_bitrate = 1e-3*bits_min*sample_rate/decode_info->frameSize;
    bits_var = bits2 / count - total_bits*total_bits/(count*(double)count);
    bitrate_std = 1e-3*sqrt(bits_var)*sample_rate/decode_info->frameSize;

    free(fdk_aac_input_buffer);
    free(outdata);
    free(fbytes);
    if (fbitstream)
        fclose(fbitstream);

    if (fout_wav)
        wav_write_close(fout_wav);
#ifdef DUMP_BITRATE
    if (fbit_wav)
      wav_write_close(fbit_wav);
#endif

    AudioX_aacDecoder_Close(fdkaac_dec_handle_);
    printf("%d frames decoded.\n", count);

    average_dec_rtf_ns = ((dec_total_rtf_ns)/(1000000*framesize_ms)) / count;
    fprintf (stderr, "average dec rtf(ms/ms):             %7.5f\n",average_dec_rtf_ns);
    double max_dec_rtf = (dec_max_rtf/(1000000*framesize_ms)) ;
    fprintf (stderr, "max dec rtf(ms/ms):                 %7.5f\n",max_dec_rtf);

    output_dec_stats->bits_per_sample = bits_per_sample;
    output_dec_stats->aot = aot;
    output_dec_stats->channels = channels;
    output_dec_stats->format = 1;
    output_dec_stats->framesize_ms = framesize_ms;
    output_dec_stats->sample_rate = sample_rate;
    output_dec_stats->skipDelaySamples = skipDelaySamples;
    output_dec_stats->transtype = transtype;

    fprintf(stderr, "/***********dec bitrate information***********/\n");
    fprintf(stderr, "average bitrate:             %7.3f kb/s\n", average_bitrate);
    fprintf(stderr, "maximum bitrate:             %7.3f kb/s\n", maximum_bitrate);
    fprintf(stderr, "minimum bitrate:             %7.3f kb/s\n", minimum_bitrate);
    fprintf(stderr, "bitrate standard deviation:  %7.3f kb/s\n", bitrate_std);

    output_dec_stats->average_bitrate = average_bitrate;
    output_dec_stats->maximum_bitrate = maximum_bitrate;
    output_dec_stats->minimum_bitrate = minimum_bitrate;
    output_dec_stats->bitrate_std = bitrate_std;

    if (!decode_only) {
        output_dec_stats->bitrate = input_enc_stats->bitrate;
        output_dec_stats->vbr = input_enc_stats->vbr;
        output_dec_stats->average_enc_rtf_ns = input_enc_stats->average_enc_rtf_ns;

        output_dec_stats->speech_ratio = input_enc_stats->speech_ratio;
        output_dec_stats->music_ratio = input_enc_stats->music_ratio;
        output_dec_stats->noise_ratio = input_enc_stats->noise_ratio;
        output_dec_stats->silence_ratio = input_enc_stats->silence_ratio;
#ifdef STEREO_DETECT_OPT
        output_dec_stats->fake_stereo_ratio = input_enc_stats->fake_stereo_ratio;
        output_dec_stats->inverse_stereo_ratio = input_enc_stats->inverse_stereo_ratio;
        output_dec_stats->average_spec_diff = input_enc_stats->average_spec_diff;
#endif
    } else {
        output_dec_stats->bitrate = 0;
        output_dec_stats->vbr = 0;
        output_dec_stats->average_enc_rtf_ns = 0.0000f;

        output_dec_stats->speech_ratio = 0.f;
        output_dec_stats->music_ratio = 0.f;
        output_dec_stats->noise_ratio = 0.f;
        output_dec_stats->silence_ratio = 0.f;
#ifdef STEREO_DETECT_OPT
        output_dec_stats->fake_stereo_ratio = 0.f;
        output_dec_stats->inverse_stereo_ratio = 0.f;
        output_dec_stats->average_spec_diff = 0.f;
#endif
    }
    output_dec_stats->average_dec_rtf_ns = average_dec_rtf_ns;

    if (need_output_dec_bitrate) {
        FILE *fp_stats = fopen(STATS_DEC_FILE, "at");
        if (fp_stats != NULL) {
        if (0 != fseek (fp_stats, 0, SEEK_SET)) {
            fprintf(stderr, "Could not move to start of results file %s!\n", STATS_FILE);
            exit (1);
        }
        long start = ftell (fp_stats);

        if (0 != fseek (fp_stats, 0, SEEK_END)) {
            fprintf(stderr, "Could not move to end of results file %s!\n", STATS_FILE);
            exit (1);
        }
        long end = ftell(fp_stats);

        if (start == end) {
            fprintf(fp_stats, "outfile_file_path\taverage_bps\t");
            fprintf(fp_stats, "\n");
            fflush(fp_stats);
        }
        fprintf(fp_stats, "%s\t", outfile);
        fprintf(fp_stats, "%.3f\t", output_dec_stats->average_bitrate);
        fprintf(fp_stats, "\n");
        }
        fclose(fp_stats);
    }

    return 0;
}


void usage(const char* name) {
    fprintf(stderr, "usage1: %s [-r bitrate] [-t aot] [-a afterburner] [-s sbr] [-v vbr] [-f adts] in.wav out.aac out.wav\n", name);
    fprintf(stderr, "usage2: %s [-r bitrate] [-d strategy_mode] [-t aot] [-a afterburner] [-f adts] in.wav out.aac out.wav\n", name);
    fprintf(stderr, "usage3: %s [-m 1] in.wav out.aac\n", name);
    fprintf(stderr, "usage4: %s [-m 2] in.aac out.wav\n", name);
    fprintf(stderr, "usage5: %s [-m 3] source.aac source_decoded.wav transcode.aac\n", name);
    fprintf(stderr, "usage6: %s [-m 4] source.aac source_decoded.wav transcode.aac transcode_decoded.wav\n", name);
    fprintf(stderr, "Supported AOTs:\t\tvbr:\n");
    fprintf(stderr, "\t2\tAAC-LC\t\t\t0\tfor CBR(default)\n");
    fprintf(stderr, "\t5\tHE-AAC\t\t\t1\tfor vbr 1 about 32 kbps/channel\n");
    fprintf(stderr, "\t29\tHE-AAC v2\t\t2\tfor vbr 2 about 40 kbps/channel\n");
    fprintf(stderr, "\t23\tAAC-LD\t\t\t3\tfor vbr 3 about 48-56 kbps/channel\n");
    fprintf(stderr, "\t39\tAAC-ELD\t\t\t4\tfor vbr 4 about 64 kbps/channel\n");
    fprintf(stderr, "f: transport format, 1 adts, others raw.\n");
    fprintf(stderr, "m: process mode, 0 encode/decode, 1 encode_only, 2 decode_only, 3 decode/encode(transcode), 3 decode/encode/decode(transcode).\n");
    fprintf(stderr, "d: down bitrate strategy mode, 0 turn off, 1 mode_1 (conservative), 2 mode_2 (mid), 3 mode_3 (aggressive).\n");
}

int get_prcocess_mode(int mode) {
    int process_mode = 0;
    if (mode < 0) {
        return process_mode;
    } else if (mode >=0 && mode <=4) {
        return mode;
    } else {
        return 4;
    }
}

#if defined(_WIN32) || defined(__APPLE__)
static void run_peaq_func(int run_peaq, int encode_only, int decode_only, const EncStats* input_stats, 
                        const char* infile, const char* outfile) {
    if (run_peaq && !encode_only && !decode_only) {
        fprintf(stderr, "/**************mos information************/\n");
        float peaq_score = 0.0;
        FILE *fp_stream = NULL;
        bool headerless_file = false;
        char str_sr[20] = {0};
#ifdef _WIN32
        char buff_cmd[1024] = "..\\tests\\peaq_bin\\peaq.exe ";
#elif __APPLE__
        char buff_cmd[1024] = "peaq ";
#endif
        if(headerless_file) {
            // below cmd is for header less file.
            // peaq_mac -P "integer16, 0, 48000, native, 1, default" es01_l_48k.pcm es01_l_48k_48kbps_opus_audio_plc0.pcm
            char str_sr[20] = {0};
            strcat(buff_cmd, " -P \"integer16, 0, ");
            sprintf(str_sr, "%d", input_stats->sample_rate);
            strcat(buff_cmd, str_sr);
            strcat(buff_cmd, ", native, ");
            sprintf(str_sr, "%d", input_stats->channels);
            strcat(buff_cmd, str_sr);
            strcat(buff_cmd, ", default\" ");
        }

        if (input_stats->sample_rate != 48000) {
          FILE *fp_tmp = NULL;
          const char *tmp_infile = "tmp_infile_48kHz.wav";
          const char *tmp_outfile = "tmp_outfile_48kHz.wav";

          char buff_resample_cmd1[1024] = "/opt/homebrew/bin/sox -r 48000 ";
          char buff_resample_cmd2[1024] = "/opt/homebrew/bin/sox -r 48000 ";

          strcat(buff_resample_cmd1, infile);
          strcat(buff_resample_cmd1, " ");
          strcat(buff_resample_cmd1, tmp_infile);

          strcat(buff_resample_cmd2, outfile);
          strcat(buff_resample_cmd2, " ");
          strcat(buff_resample_cmd2, tmp_outfile);
          fp_tmp = POPEN(buff_resample_cmd1, "r");
          PCLOSE(fp_tmp);
          fp_tmp = POPEN(buff_resample_cmd2, "r");
          PCLOSE(fp_tmp);
          
#ifdef TEST_DOWNDIX_PEAQ
          // Can remix to 1 channel, then test PEAQ.
          const char *tmp_infile_1 = "tmp_infile_48kHz_1.wav";
          const char *tmp_outfile_1 = "tmp_outfile_48kHz_1.wav";
          char buff_downmix_cmd1[1024] = "/usr/local/bin/ffmpeg -loglevel quiet -y -i ";
          char buff_downmix_cmd2[1024] = "/usr/local/bin/ffmpeg -loglevel quiet -y -i ";
          strcat(buff_downmix_cmd1, tmp_infile);
          strcat(buff_downmix_cmd1, " -ac 1 ");
          strcat(buff_downmix_cmd1, tmp_infile_1);

          strcat(buff_downmix_cmd2, tmp_outfile);
          strcat(buff_downmix_cmd2, " -ac 1 ");
          strcat(buff_downmix_cmd2, tmp_outfile_1);
          fp_tmp = POPEN(buff_downmix_cmd1, "r");
          PCLOSE(fp_tmp);
          fp_tmp = POPEN(buff_downmix_cmd2, "r");
          PCLOSE(fp_tmp);

          strcat(buff_cmd, tmp_infile_1);
          strcat(buff_cmd, " ");
          strcat(buff_cmd, tmp_outfile_1); 
#else
          strcat(buff_cmd, tmp_infile);
          strcat(buff_cmd, " ");
          strcat(buff_cmd, tmp_outfile);
          fp_stream = POPEN(buff_cmd, "r");
#endif
        } else {
          strcat(buff_cmd, infile);
          strcat(buff_cmd, " ");
          strcat(buff_cmd, outfile);
          fp_stream = POPEN(buff_cmd, "r");
        }

        char pre_buf[1000];
        if (NULL == fp_stream) {
            fprintf(stderr, "execute command failed: %s\n", strerror(errno));
        } else {
            //get last line of the print information.
            while (NULL != fgets(buff_cmd, sizeof(buff_cmd), fp_stream)) {
                memcpy(pre_buf, buff_cmd, sizeof(pre_buf));
            }
            PCLOSE(fp_stream);
            {
                //get peaq result.
                char a[20], b[20], c[20], d[20];
                sscanf(pre_buf, "%s %s %s %s\n", a, b, c, d);
                peaq_score = (float) (atof(d)) + 5.0f;
            }
            fprintf(stderr, "peaq score:                   %7.4f\n", peaq_score);
//            FILE *fp_stats = fopen(STATS_FILE, "at");
//            if (fp_stats != NULL) {
//                fprintf(fp_stats, "%.3f\t", peaq_score);
//                fprintf(fp_stats, "%.1f\t", 1e-3 * bitrate_bps);
//                fprintf(fp_stats, "%.3f\t", (average_bitrate - 1e-3 * bitrate_bps));
//                fprintf(fp_stats, "%7.5f\t", average_enc_rtf_ns);
//                if (!encode_only) fprintf(fp_stats, "%7.5f\t", average_dec_rtf_ns);
//                fprintf(fp_stats, "\n");
//            }
//            fclose(fp_stats);
        }

        FILE *fp_stats = fopen(STATS_FILE, "at");
        if (fp_stats != NULL) {
        if (0 != fseek (fp_stats, 0, SEEK_SET)) {
            fprintf(stderr, "Could not move to start of results file %s!\n", STATS_FILE);
            exit (1);
        }
        long start = ftell (fp_stats);

        if (0 != fseek (fp_stats, 0, SEEK_END)) {
            fprintf(stderr, "Could not move to end of results file %s!\n", STATS_FILE);
            exit (1);
        }
        long end = ftell(fp_stats);

        if (start == end) {
            fprintf(fp_stats, "in_file_path\tsampling_rate\tchannels\taot\ttrans_type\tvbr_mode\t"
                    "target_bps\taverage_bps\tmax_bps\tmin_bps\tpeaq_mos\tenc_rtf\tdec_rtf\t"
                    "speech_ratio\tmusic_ratio\tnoise_ratio\tsilence_ratio\t"
#ifdef STEREO_DETECT_OPT
                    "fake_stereo_ratio\tinverse_stereo_ratio\taverage_spec_diff\t"
#endif
                    );
            fprintf(fp_stats, "\n");
            fflush(fp_stats);
        }
        fprintf(fp_stats, "%s\t", infile);
        fprintf(fp_stats, "%d\t", input_stats->sample_rate);
        fprintf(fp_stats, "%d\t", input_stats->channels);
        fprintf(fp_stats, "%s\t", aot_to_string(input_stats->aot));
        fprintf(fp_stats, "%s\t", transtype_to_string(input_stats->transtype));
        fprintf(fp_stats, "%d\t", input_stats->vbr);
        fprintf(fp_stats, "%d\t", input_stats->vbr == 0 ? input_stats->bitrate : 0);
        fprintf(fp_stats, "%.3f\t", input_stats->average_bitrate);
        fprintf(fp_stats, "%.3f\t", input_stats->maximum_bitrate);
        fprintf(fp_stats, "%.3f\t", input_stats->minimum_bitrate);
        fprintf(fp_stats, "%.3f\t", peaq_score);
        fprintf(fp_stats, "%7.5f\t", input_stats->average_enc_rtf_ns);
        fprintf(fp_stats, "%7.5f\t", input_stats->average_dec_rtf_ns);
        fprintf(fp_stats, "%.2f\t", input_stats->speech_ratio);
        fprintf(fp_stats, "%.2f\t", input_stats->music_ratio);
        fprintf(fp_stats, "%.2f\t", input_stats->noise_ratio);
        fprintf(fp_stats, "%.2f\t", input_stats->silence_ratio);
#ifdef STEREO_DETECT_OPT
        fprintf(fp_stats, "%.2f\t", input_stats->fake_stereo_ratio);
        fprintf(fp_stats, "%.2f\t", input_stats->inverse_stereo_ratio);
        fprintf(fp_stats, "%.4f\t", input_stats->average_spec_diff);
#endif
        fprintf(fp_stats, "\n");
        }
        fclose(fp_stats);

    }
}
#endif

int main(int argc, char *argv[]) {
    int bitrate = 64000;
    int ch;
    const char *infile, *bitstreamfile, *outfile;
    int aot = 2;
    int afterburner = 1;
    int eld_sbr = 0;
    int vbr = 0;
    TRANSPORT_TYPE transtype = TT_MP4_ADTS;
    #ifdef ABR_OPT
    int abr_mode = 0;
    int low_cplx_opt = 0;
    //int use_abr_close_loop = 0;
    #endif
    int decode_only = 0;
    int encode_only = 0;
    int process_mode = 0;
    int process_transcode = 0;
    int transcode_to_wav = 0;
    const char  *transcode_source_aac_decoded_file;
    FILE *f_transcode_source_aac_decoded_file = NULL;
#ifdef BITRATE_STRATEGY_OPT
    int strategy_mode = 0;
#endif
    //skip delay samples for decode file for stereo input.

#ifdef FDKAAC_DEMO_IOS
	std::string outfile_str, infile_str, bitstreamfile_str, transcode_source_aac_decoded_file_str;
    std::string sandbox_dir = GetIOSDocumentsDirPath();
#endif
#ifdef ABR_OPT
    int args = 1;
    while (args < argc) {
      if (strcmp(argv[args], "-abr") == 0) {
        args++;
        abr_mode = atoi(argv[args]);
      } else if (strcmp(argv[args], "-low_cplx_opt") == 0) {
        args++;
        low_cplx_opt = atoi(argv[args]);
      } else if (strcmp(argv[args], "-r") == 0) {
        args++;
        bitrate = atoi(argv[args]);
      } else if (strcmp(argv[args], "-t") == 0) {
        args++;
        aot = atoi(argv[args]);
      } else if (strcmp(argv[args], "-a") == 0) {
        args++;
        afterburner = atoi(argv[args]);
      } else if (strcmp(argv[args], "-s") == 0) {
        args++;
        eld_sbr = atoi(argv[args]);
      } else if (strcmp(argv[args], "-v") == 0) {
        args++;
        vbr = atoi(argv[args]);
      } else if (strcmp(argv[args], "-f") == 0) {
        args++;
        transtype = get_transtype(atoi(argv[args]));
      } else if (strcmp(argv[args], "-m") == 0) {
        args++;
        process_mode = get_prcocess_mode(atoi(argv[args]));
      } else if (strcmp(argv[args], "-d") == 0) {
        args++;
        strategy_mode = atoi(argv[args]);
      }
      args++;
    }
    if (abr_mode == 0) {
        if (low_cplx_opt > 3) {
            low_cplx_opt=3;
        }
    }
#else
    //while ((ch = getopt(argc, argv, "r:t:a:s:v:f:m:d:b:")) != -1) {
#ifdef BITRATE_STRATEGY_OPT
while ((ch = getopt(argc, argv, "r:t:a:s:v:f:m:d:")) != -1) {
#else
while ((ch = getopt(argc, argv, "r:t:a:s:v:f:m:")) != -1) {
#endif
        switch (ch) {
            case 'r':
                bitrate = atoi(optarg);
                break;
            case 't':
                aot = atoi(optarg);
                break;
            case 'a':
                afterburner = atoi(optarg);
                break;
            case 's':
                eld_sbr = atoi(optarg);
                break;
            case 'v':
                vbr = atoi(optarg);
                break;
            case 'f':
                transtype = get_transtype(atoi(optarg));
                break;
            case 'm':
                process_mode = get_prcocess_mode(atoi(optarg));
                break;
#ifdef ABR_OPT 
             case 'b':
                abr_open_loop_bps = atoi(optarg);
                //vbr = 5; 
                break;
             //case 'k':
                //use_abr_close_loop = atoi(optarg);
                //break;
#endif
#ifdef BITRATE_STRATEGY_OPT
            case 'd':
                strategy_mode = atoi(optarg);
                break;
#endif
            case '?':
            default:
                usage(argv[0]);
                return 1;
        }
    }
#endif
    if (process_mode == 0) {
        process_transcode = 0;
        encode_only = 0;
        decode_only = 0;
    } else if (process_mode == 1) {
        process_transcode = 0;
        encode_only = 1;
        decode_only = 0;
    } else if (process_mode == 2) {
        process_transcode = 0;
        encode_only = 0;
        decode_only = 1;
    } else if (process_mode == 3) {
        process_transcode = 1;
        transcode_to_wav = 0;
        encode_only = 0;
        decode_only = 0;
    } else {
        process_transcode = 1;
        transcode_to_wav = 1;
        encode_only = 0;
        decode_only = 0;
    }

    if (encode_only && decode_only) {
        usage(argv[0]);
        return 1;
    } else if (decode_only || encode_only) {
#ifdef ABR_OPT
        if (0) {
#else
        if (argc - optind != 2) {
#endif
            usage(argv[0]);
            return 1;
        }
    } else {
        if (process_transcode == 0 || transcode_to_wav == 0) {
#ifdef ABR_OPT
            if (0) {
#else
            if (argc - optind != 3) {
#endif
                usage(argv[0]);
                return 1;
            }
        } else {
#ifdef ABR_OPT
                if (0) {
#else
                if (argc - optind < 4) {
#endif
                usage(argv[0]);
                return 1;
            }
        }
    }

    if (!decode_only && !encode_only) {
        if(process_transcode) {
#ifdef FDKAAC_DEMO_IOS
            infile_str = sandbox_dir + std::string("/") + std::string(argv[optind]);
            infile = infile_str.c_str();
            transcode_source_aac_decoded_file_str = sandbox_dir + std::string("/") + std::string(argv[optind+1]);
            transcode_source_aac_decoded_file = transcode_source_aac_decoded_file_str.c_str();
            bitstreamfile_str = sandbox_dir + std::string("/") + std::string(argv[optind+2]);
            bitstreamfile = bitstreamfile_str.c_str();
            outfile_str = sandbox_dir + std::string("/") + std::string(argv[optind+3]);
            outfile = outfile_str.c_str();
#else
#ifdef ABR_OPT
            if (transcode_to_wav) {
                infile = argv[argc-4];
                transcode_source_aac_decoded_file = argv[argc-3];
                bitstreamfile = argv[argc-2];
                outfile = argv[argc-1];
            }else{
                infile = argv[argc-3];
                transcode_source_aac_decoded_file = argv[argc-2];
                bitstreamfile = argv[argc-1];
            }
#else
            infile = argv[optind];      // source aac file
            transcode_source_aac_decoded_file = argv[optind+1]; // source aac decoded file
            bitstreamfile = argv[optind+2]; // transcode aac file
            if (transcode_to_wav) {
                outfile = argv[optind + 3];   //transcode aac decoded file
            }
#endif
#endif
        } else {
#ifdef FDKAAC_DEMO_IOS
        infile_str = sandbox_dir + std::string("/") + std::string(argv[optind]);
        infile = infile_str.c_str();
        bitstreamfile_str = sandbox_dir + std::string("/") + std::string(argv[optind+1]);
        bitstreamfile = bitstreamfile_str.c_str();
        outfile_str = sandbox_dir + std::string("/") + std::string(argv[optind+2]);
        outfile = outfile_str.c_str();
#else
#ifdef ABR_OPT
            infile = argv[argc-3];
            bitstreamfile = argv[argc-2];
            outfile = argv[argc-1];
#else
        infile = argv[optind];
        bitstreamfile = argv[optind + 1];
        outfile = argv[optind + 2];
#endif
#endif
        }
    } else if (encode_only) {
#ifdef FDKAAC_DEMO_IOS
        infile_str = sandbox_dir + std::string("/") + std::string(argv[optind]);
        infile = infile_str.c_str();
        bitstreamfile_str = sandbox_dir + std::string("/") + std::string(argv[optind+1]);
        bitstreamfile = bitstreamfile_str.c_str();
#else
#ifdef ABR_OPT
        infile = argv[argc-2];
        bitstreamfile = argv[argc-1];
#else
        infile = argv[optind];
        bitstreamfile = argv[optind + 1];
#endif
#endif
    } else {
#ifdef FDKAAC_DEMO_IOS
        bitstreamfile_str = sandbox_dir + std::string("/") + std::string(argv[optind+1]);
        bitstreamfile = bitstreamfile_str.c_str();
        outfile_str = sandbox_dir + std::string("/") + std::string(argv[optind+2]);
        outfile = outfile_str.c_str();
#else
#ifdef ABR_OPT
        bitstreamfile = argv[argc-2];
        outfile = argv[argc-1];
#else
        bitstreamfile = argv[optind];
        outfile = argv[optind + 1];
#endif
#endif
    }

    EncStats enc_stats, dec_stats;
    int enc_res = 0, dec_res = 0;
    int run_peaq = true;
    if (process_mode < 3) {
        if (!decode_only) {
#ifdef ABR_OPT
            enc_res = process_encode(infile, aot, eld_sbr, vbr, bitrate, transtype,
                afterburner, strategy_mode, abr_mode, low_cplx_opt,
                bitstreamfile,
                &enc_stats);
#else
#ifdef BITRATE_STRATEGY_OPT
            enc_res = process_encode(infile, aot, eld_sbr, vbr, bitrate, transtype, afterburner, strategy_mode, bitstreamfile, &enc_stats);
#else
            enc_res = process_encode(infile, aot, eld_sbr, vbr, bitrate, transtype, afterburner, bitstreamfile, &enc_stats);
#endif
#endif
            if (enc_res > 0) {
                return 1;
            }
        }

        if(!encode_only) {
            dec_res = process_decode(bitstreamfile, decode_only, outfile, &enc_stats, &dec_stats);
            if (dec_res > 0) {
                return 1;
            }
        }
#if defined(__APPLE__)
        run_peaq_func(run_peaq, encode_only, decode_only, &dec_stats, infile, outfile);
#endif
    } else {
        // do decode
        dec_res = process_decode(infile, 1, transcode_source_aac_decoded_file, &enc_stats, &dec_stats);
        if (dec_res > 0) {
            return 1;
        }
        // do encode
#ifdef ABR_OPT 
        enc_res = process_encode(transcode_source_aac_decoded_file, aot, eld_sbr, vbr, bitrate, transtype, afterburner, strategy_mode, abr_mode, low_cplx_opt, bitstreamfile, &enc_stats);
#else
#ifdef BITRATE_STRATEGY_OPT
        enc_res = process_encode(transcode_source_aac_decoded_file, aot, eld_sbr, vbr, bitrate, transtype, afterburner, strategy_mode, bitstreamfile, &enc_stats);;
#else
        enc_res = process_encode(transcode_source_aac_decoded_file, aot, eld_sbr, vbr, bitrate, transtype, afterburner, bitstreamfile, &enc_stats);;
#endif
#endif
        if (enc_res > 0) {
            return 1;
        }
        if(transcode_to_wav) {
            // do decode
            dec_res = process_decode(bitstreamfile, decode_only, outfile, &enc_stats, &dec_stats);
            if (dec_res > 0) {
                return 1;
            }
#if defined(_WIN32) || defined(__APPLE__)
            run_peaq_func(run_peaq, encode_only, decode_only, &dec_stats, transcode_source_aac_decoded_file, outfile);
#endif
        }
    }
    
    return 0;
}

