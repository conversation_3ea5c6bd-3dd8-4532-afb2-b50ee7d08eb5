#!/bin/bash
# build fdk-aac gtest

rm -rf output
rm -rf cmake-build-fdkaac-gtest
mkdir -p output
mkdir -p cmake-build-fdkaac-gtest
cd cmake-build-fdkaac-gtest
rm -f CMakeCache.txt
cmake .. -DFDK_AAC_BUILD_TEST=ON \
        -DINSTALL_GTEST=OFF \
        -DBUILD_GMOCK=OFF \
        -DCMAKE_CXX_STANDARD=17 \
        -DBUILD_SHARED_LIBS=OFF \
        -DCMAKE_BUILD_TYPE=Release \
        -DBUILD_PROGRAMS=OFF
make -j10
if [ ! -f "./aac_unittest" ];then
    echo "build aac unittest failed"
    exit 1
fi

./aac_unittest