.BuildX_Cloud_Build_Branch: &BuildX_Cloud_Build_Branch
  BX_SCRIPT_REF: main_algos

.BuildX_Cloud_Build_Common_Config: &BuildX_Cloud_Build_Common_Config
  <<: *BuildX_Cloud_Build_Branch
  BX_PLUGIN_ROUTING_STRATEGY: v3
  BX_REPO_GIT_SSH_URL: ******************:videoarch/AudioXAAC.git
  BX_REPO_BUILD_BRANCH: ${WORKFLOW_REPO_BRANCH}

.BuildX_Android_Component_Cloud_Build_Config: &BuildX_Android_Component_Cloud_Build_Config
  template_config_id: 65245

.BuildX_Harmony_Component_Cloud_Build_Config: &BuildX_Harmony_Component_Cloud_Build_Config
  template_config_id: 65247

.BuildX_Linux_x86_Component_Cloud_Build_Config: &BuildX_Linux_x86_Component_Cloud_Build_Config
  template_config_id: 65407

.BuildX_Linux_x86_CUDA_TensorRT_Cloud_Build_Config: &BuildX_Linux_x86_CUDA_TensorRT_Cloud_Build_Config
  template_config_id: 65408

.BuildX_Windows_x86_Component_Cloud_Build_Config: &BuildX_Windows_x86_Component_Cloud_Build_Config
  template_config_id: 65414

.BuildX_iOS_Xcode15_Component_Cloud_Build_Config: &BuildX_iOS_Xcode15_Component_Cloud_Build_Config
  template_config_id: 65406

.BuildX_MacOS_Compile_iOS_Dolphin_Cache: &BuildX_MacOS_Compile_iOS_Dolphin_Cache
  template_config_id: 65421

.BuildX_macOS_Component_Cloud_Build_Config: &BuildX_macOS_Component_Cloud_Build_Config
  template_config_id: 65226

.BuildX_Windows_VS2019_ASAN: &BuildX_Windows_VS2019_ASAN
  template_config_id: 65415

.BX_MacOS_x64_Xcode14_Common: &BX_MacOS_x64_Xcode14_Common
  template_config_id: 65419

.BX_MacOS_arm64_Xcode14_Common: &BX_MacOS_arm64_Xcode14_Common
  template_config_id: 65420

.BX_Linux_x64_Common: &BX_Linux_x64_Common
  template_config_id: 65417

.BuildX_Linux_ARM64_AMD_GPU: &BuildX_Linux_ARM64_AMD_GPU
  template_config_id: 65418

windows_encode_test_x86_64:
  <<: *BuildX_Windows_VS2019_ASAN
  <<: *BuildX_Cloud_Build_Common_Config
  BX_ACTION_NAME: ci.windows.encode_test
  BX_OS_PLATFORM: windows
  CONFIGURATION:
    VENDOR: case
    TRIGGER_SOURCE: CI

windows_build_lib_all:
  <<: *BuildX_Windows_VS2019_ASAN
  <<: *BuildX_Cloud_Build_Common_Config
  BX_ACTION_NAME: ci.windows.build_lib
  BX_OS_PLATFORM: windows
  CONFIGURATION:
    VENDOR: case
    TRIGGER_SOURCE: CI

build_linux_lib_x86_64:
  <<: *BX_Linux_x64_Common
  <<: *BuildX_Cloud_Build_Common_Config
  BX_ACTION_NAME: ci.linux.build_lib
  BX_OS_PLATFORM: linux
  CONFIGURATION:
    VENDOR: case
    TRIGGER_SOURCE: CI

build_linux_lib_arm64:
  <<: *BuildX_Linux_ARM64_AMD_GPU
  <<: *BuildX_Cloud_Build_Common_Config
  BX_ACTION_NAME: ci.linux.build_lib
  BX_OS_PLATFORM: linux
  CONFIGURATION:
    VENDOR: case
    BX_EXTRA_CMAKE_ARGS: '{"BX_ARCH": "aarch64"}'
    TRIGGER_SOURCE: CI

linux_encode_test_x86_64:
  <<: *BX_Linux_x64_Common
  <<: *BuildX_Cloud_Build_Common_Config
  BX_ACTION_NAME: ci.linux.encode_test
  BX_OS_PLATFORM: linux
  CONFIGURATION:
    VENDOR: case
    TRIGGER_SOURCE: CI

linux_encode_test_arm64:
  <<: *BuildX_Linux_ARM64_AMD_GPU
  <<: *BuildX_Cloud_Build_Common_Config
  BX_ACTION_NAME: ci.linux.encode_test
  BX_OS_PLATFORM: linux
  CONFIGURATION:
    VENDOR: case
    BX_EXTRA_CMAKE_ARGS: '{"BX_ARCH": "aarch64"}'
    TRIGGER_SOURCE: CI

linux_encode_quality_test_x86_64:
  <<: *BX_Linux_x64_Common
  <<: *BuildX_Cloud_Build_Common_Config
  BX_ACTION_NAME: ci.linux.encode_quality_test
  BX_OS_PLATFORM: linux
  CONFIGURATION:
    VENDOR: case
    TRIGGER_SOURCE: CI

# linux_signal_analysis_test_x86_64:
#   <<: *BX_Linux_x64_Common
#   <<: *BuildX_Cloud_Build_Common_Config
#   BX_ACTION_NAME: ci.linux.signal_analysis_test
#   BX_OS_PLATFORM: linux
#   CONFIGURATION:
#     VENDOR: case
#     TRIGGER_SOURCE: CI

# linux_signal_analysis_test_arm64:
#   <<: *BuildX_Linux_ARM64_AMD_GPU
#   <<: *BuildX_Cloud_Build_Common_Config
#   BX_ACTION_NAME: ci.linux.signal_analysis_test
#   BX_OS_PLATFORM: linux
#   CONFIGURATION:
#     VENDOR: case
#     BX_EXTRA_CMAKE_ARGS: '{"BX_ARCH": "aarch64"}'
#     TRIGGER_SOURCE: CI