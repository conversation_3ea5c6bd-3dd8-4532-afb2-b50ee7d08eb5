#include "encoder_impl.h"
#include "stdio.h"

#ifdef AACENCODER_LIB_VL0
#define FDKENC_VER_AT_LEAST(vl0, vl1) \
    ((AACENCODER_LIB_VL0 > vl0) || \
     (AACENCODER_LIB_VL0 == vl0 && AACENCODER_LIB_VL1 >= vl1))
#else
#define FDKENC_VER_AT_LEAST(vl0, vl1) 0
#endif

int AudioX_aac_encode_close(EncoderContext *enc_ctx)
{
    if (enc_ctx->handle)
        AudioX_aacEncClose(&enc_ctx->handle);
    return 0;
}

AACENC_ERROR AudioX_aac_encode_init(EncoderContext *enc_ctx)
{
    AACENC_InfoStruct info = { 0 };
    CHANNEL_MODE mode;
    AACENC_ERROR err = AACENC_OK;
    int sce = 0, cpe = 0;

    if ((err = AudioX_aacEncOpen(&enc_ctx->handle, 0, enc_ctx->channels)) != AACENC_OK) {
        fprintf(stderr, "Unable to open the encoder: %s\n",
               AudioX_aac_get_error(err));
        goto error;
    }

    if ((err = AudioX_aacEncoder_SetParam(enc_ctx->handle, AACENC_AOT, enc_ctx->aot)) != AACENC_OK) {
        fprintf(stderr, "Unable to set the AOT %d: %s\n",
               enc_ctx->aot, AudioX_aac_get_error(err));
        goto error;
    }

    if (enc_ctx->aot == AOT_ER_AAC_ELD && enc_ctx->eld_sbr) {
        if ((err = AudioX_aacEncoder_SetParam(enc_ctx->handle, AACENC_SBR_MODE,
                                       1)) != AACENC_OK) {
            fprintf(stderr, "Unable to enable SBR for ELD: %s\n",
                   AudioX_aac_get_error(err));
            goto error;
        }
    }

    if ((err = AudioX_aacEncoder_SetParam(enc_ctx->handle, AACENC_SAMPLERATE,
                                   enc_ctx->samplerate)) != AACENC_OK) {
        fprintf(stderr, "Unable to set the sample rate %d: %s\n",
               enc_ctx->samplerate, AudioX_aac_get_error(err));
        goto error;
    }

    switch (enc_ctx->channels) {
    case 1: mode = MODE_1;       sce = 1; cpe = 0; break;
    case 2:
#if FDKENC_VER_AT_LEAST(4, 0) // 4.0.0
      // (profile + 1) to map from profile range to AOT range
      if (enc_ctx->aot == AOT_ER_AAC_ELD && enc_ctx->eld_v2) {
          if ((err = AudioX_aacEncoder_SetParam(enc_ctx->handle, AACENC_CHANNELMODE,
                                         128)) != AACENC_OK) {
              fprintf(stderr, "Unable to enable ELDv2: %s\n",
                     AudioX_aac_get_error(err));
              goto error;
          } else {
            mode = MODE_212;
            sce = 1;
            cpe = 0;
          }
      } else
#endif
      {
        mode = MODE_2;
        sce = 0;
        cpe = 1;
      }
      break;
    case 3: mode = MODE_1_2;     sce = 1; cpe = 1; break;
    case 4: mode = MODE_1_2_1;   sce = 2; cpe = 1; break;
    case 5: mode = MODE_1_2_2;   sce = 1; cpe = 2; break;
    case 6: mode = MODE_1_2_2_1; sce = 2; cpe = 2; break;
/* The version macro is introduced the same time as the 7.1 support, so this
   should suffice. */
#if FDKENC_VER_AT_LEAST(3, 4) // 3.4.12
    // case 8:
    //     sce = 2;
    //     cpe = 3;
    //     if (enc_ctx->channel_layout == AV_CH_LAYOUT_7POINT1) {
    //         mode = MODE_7_1_REAR_SURROUND;
    //     } else {
    //         // MODE_1_2_2_2_1 and MODE_7_1_FRONT_CENTER use the same channel layout
    //         mode = MODE_7_1_FRONT_CENTER;
    //     }
    //     break;
#endif
    default:
        fprintf(stderr,
               "Unsupported number of channels %d\n", enc_ctx->channels);
        goto error;
    }

    if ((err = AudioX_aacEncoder_SetParam(enc_ctx->handle, AACENC_CHANNELMODE,
                                   mode)) != AACENC_OK) {
        fprintf(stderr,
               "Unable to set channel mode %d: %s\n", mode, AudioX_aac_get_error(err));
        goto error;
    }

    if ((err = AudioX_aacEncoder_SetParam(enc_ctx->handle, AACENC_CHANNELORDER,
                                   1)) != AACENC_OK) {
        fprintf(stderr,
               "Unable to set wav channel order %d: %s\n",
               mode, AudioX_aac_get_error(err));
        goto error;
    }

    if (enc_ctx->vbr) {
        int mode = enc_ctx->vbr;
        if (mode <  1 || mode > 5) {
            fprintf(stderr,
                   "VBR quality %d out of range, should be 1-5\n", mode);
            mode = av_clip_c(mode, 1, 5);
        }
        fprintf(stderr,
               "Note, the VBR setting is unsupported and only works with "
               "some parameter combinations\n");
        if ((err = AudioX_aacEncoder_SetParam(enc_ctx->handle, AACENC_BITRATEMODE,
                                       mode)) != AACENC_OK) {
            fprintf(stderr, "Unable to set the VBR bitrate mode %d: %s\n",
                   mode, AudioX_aac_get_error(err));
            goto error;
        }
    } else {
        if (enc_ctx->bitrate <= 0) {
            if (enc_ctx->aot == AOT_PS) {
                sce = 1;
                cpe = 0;
            }
            enc_ctx->bitrate = (96*sce + 128*cpe) * enc_ctx->samplerate / 44;
            if (enc_ctx->aot == AOT_SBR ||
                enc_ctx->aot == AOT_PS ||
                enc_ctx->eld_sbr)
                enc_ctx->bitrate /= 2;
        }
        if ((err = AudioX_aacEncoder_SetParam(enc_ctx->handle, AACENC_BITRATE,
                                       enc_ctx->bitrate)) != AACENC_OK) {
            fprintf(stderr, "Unable to set the bitrate %d: %s\n",
                   enc_ctx->bitrate, AudioX_aac_get_error(err));
            goto error;
        }
//        if ((err = AudioX_aacEncoder_SetParam(enc_ctx->handle, AACENC_BITRATE_STRATEGY_MODE,
//                                       enc_ctx->bitrate_mode)) != AACENC_OK) {
//            fprintf(stderr, "Unable to set the bitrate mode %d: %s\n",
//                   enc_ctx->bitrate_mode, AudioX_aac_get_error(err));
//            goto error;
//        }
//        if(enc_ctx->abr) {
//            if ((err = AudioX_aacEncoder_SetParam(enc_ctx->handle, AACENC_ABR_OPEN_LOOP,
//                                       enc_ctx->abr)) != AACENC_OK) {
//                fprintf(stderr, "Unable to set the abr rate %d: %s\n",
//                    enc_ctx->abr, AudioX_aac_get_error(err));
//                goto error;
//            }
//        }
    }

    /* Choose bitstream format - if global header is requested, use
     * raw access units, otherwise use ADTS. */
    if ((err = AudioX_aacEncoder_SetParam(enc_ctx->handle, AACENC_TRANSMUX,
                                   enc_ctx->transtype)) != AACENC_OK) {
        fprintf(stderr, "Unable to set the transmux format: %s\n",
               AudioX_aac_get_error(err));
        goto error;
    }

    if (enc_ctx->latm && enc_ctx->header_period) {
        if ((err = AudioX_aacEncoder_SetParam(enc_ctx->handle, AACENC_HEADER_PERIOD,
                                       enc_ctx->header_period)) != AACENC_OK) {
             fprintf(stderr, "Unable to set header period: %s\n",
                    AudioX_aac_get_error(err));
             goto error;
        }
    }

    /* If no signaling mode is chosen, use explicit hierarchical signaling
     * if using mp4 mode (raw access units, with global header) and
     * implicit signaling if using ADTS. */
    if (enc_ctx->signaling < 0)
        enc_ctx->signaling = 0;

    if ((err = AudioX_aacEncoder_SetParam(enc_ctx->handle, AACENC_SIGNALING_MODE,
                                   enc_ctx->signaling)) != AACENC_OK) {
        fprintf(stderr, "Unable to set signaling mode %d: %s\n",
               enc_ctx->signaling, AudioX_aac_get_error(err));
        goto error;
    }

    if ((err = AudioX_aacEncoder_SetParam(enc_ctx->handle, AACENC_AFTERBURNER,
                                   enc_ctx->afterburner)) != AACENC_OK) {
        fprintf(stderr, "Unable to set afterburner to %d: %s\n",
               enc_ctx->afterburner, AudioX_aac_get_error(err));
        goto error;
    }

    if (enc_ctx->cutoff_freq > 0) {
        if (enc_ctx->cutoff_freq < (enc_ctx->samplerate + 255) >> 8 || enc_ctx->cutoff_freq > 20000) {
            fprintf(stderr, "cutoff_frep valid range is %d-20000\n",
                   (enc_ctx->samplerate + 255) >> 8);
            goto error;
        }
        if ((err = AudioX_aacEncoder_SetParam(enc_ctx->handle, AACENC_BANDWIDTH,
                                       enc_ctx->cutoff_freq)) != AACENC_OK) {
            fprintf(stderr, "Unable to set the encoder bandwidth to %d: %s\n",
                   enc_ctx->cutoff_freq, AudioX_aac_get_error(err));
            goto error;
        }
    }

    if ((err = AudioX_aacEncEncode(enc_ctx->handle, NULL, NULL, NULL, NULL)) != AACENC_OK) {
        fprintf(stderr, "Unable to initialize the encoder: %s\n",
               AudioX_aac_get_error(err));
        goto error;
    }

    if ((err = AudioX_aacEncInfo(enc_ctx->handle, &info)) != AACENC_OK) {
        fprintf(stderr, "Unable to get encoder info: %s\n",
               AudioX_aac_get_error(err));
        goto error;
    }
    enc_ctx->frame_size = info.frameLength;
    return err;
error:
    AudioX_aac_encode_close(enc_ctx);
    return err;
}

AACENC_ERROR AudioX_aac_encode_frame(EncoderContext *enc_ctx, AudioFrame *pcm_frame, AudioStream *encoded_stream)
{
    AACENC_BufDesc in_buf   = { 0 }, out_buf = { 0 };
    AACENC_InArgs  in_args  = { 0 };
    AACENC_OutArgs out_args = { 0 };
    int in_buffer_identifier = IN_AUDIO_DATA;
    int in_buffer_size, in_buffer_element_size;
    int out_buffer_identifier = OUT_BITSTREAM_DATA;
    int out_buffer_size, out_buffer_element_size;
    void *in_ptr, *out_ptr;
    int ret;
    uint8_t dummy_buf[1];
    AACENC_ERROR err;

    /* handle end-of-stream small frame and flushing */
    if (!pcm_frame) {
        /* Must be a non-null pointer, even if it's a dummy. We could use
         * the address of anything else on the stack as well. */
        in_ptr               = dummy_buf;
        in_buffer_size       = 0;

        in_args.numInSamples = -1;
    } else {
        in_ptr               = pcm_frame->pcm_data;
        in_buffer_size       = 2 * pcm_frame->channels * pcm_frame->samples_per_channel;

        in_args.numInSamples = pcm_frame->channels * pcm_frame->samples_per_channel;
    }

    in_buffer_element_size   = 2;
    in_buf.numBufs           = 1;
    in_buf.bufs              = &in_ptr;
    in_buf.bufferIdentifiers = &in_buffer_identifier;
    in_buf.bufSizes          = &in_buffer_size;
    in_buf.bufElSizes        = &in_buffer_element_size;


    out_ptr                   = encoded_stream->encoded_data;
    out_buffer_size           = sizeof(encoded_stream->encoded_data);
    out_buffer_element_size   = 1;
    out_buf.numBufs           = 1;
    out_buf.bufs              = &out_ptr;
    out_buf.bufferIdentifiers = &out_buffer_identifier;
    out_buf.bufSizes          = &out_buffer_size;
    out_buf.bufElSizes        = &out_buffer_element_size;

    if ((err = AudioX_aacEncEncode(enc_ctx->handle, &in_buf, &out_buf, &in_args,
                            &out_args)) != AACENC_OK) {
        if (!pcm_frame && err == AACENC_ENCODE_EOF)
            return AACENC_OK;
        fprintf(stderr, "Unable to encode frame: %s\n",
               AudioX_aac_get_error(err));
        return err;
    }

    if (!out_args.numOutBytes) {
        encoded_stream->encoded_len = 0;
        return err;
    } else {
        encoded_stream->encoded_len = out_args.numOutBytes;
    }
    return err;
}

const char *AudioX_aac_get_error(AACENC_ERROR err)
{
    switch (err) {
    case AACENC_OK:
        return "No error";
    case AACENC_INVALID_HANDLE:
        return "Invalid handle";
    case AACENC_MEMORY_ERROR:
        return "Memory allocation error";
    case AACENC_UNSUPPORTED_PARAMETER:
        return "Unsupported parameter";
    case AACENC_INVALID_CONFIG:
        return "Invalid config";
    case AACENC_INIT_ERROR:
        return "Initialization error";
    case AACENC_INIT_AAC_ERROR:
        return "AAC library initialization error";
    case AACENC_INIT_SBR_ERROR:
        return "SBR library initialization error";
    case AACENC_INIT_TP_ERROR:
        return "Transport library initialization error";
    case AACENC_INIT_META_ERROR:
        return "Metadata library initialization error";
    case AACENC_ENCODE_ERROR:
        return "Encoding error";
    case AACENC_ENCODE_EOF:
        return "End of file";
    default:
        return "Unknown error";
    }
}