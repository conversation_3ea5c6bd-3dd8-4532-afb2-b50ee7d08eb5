#ifndef HELPERS_H_
#define HELPERS_H_

#include <stdio.h>
#include <stdlib.h>
#include <math.h>
#include <time.h>
#include <string>

// Return thread ID as a string.
std::string GetThreadId();

// Return thread ID as string suitable for debug logging.
std::string GetThreadInfo();

// Returns [NSThread currentThread] description as string.
// Example: <NSThread: 0x170066d80>{number = 1, name = main}
std::string GetCurrentThreadDescription();

#if defined(FDKAAC_DEMO_IOS)
// Returns the sand box dir of ios app.
std::string GetIOSDocumentsDirPath();

// Returns the current name of the operating system.
std::string GetSystemName();

// Returns the current version of the operating system as a string.
std::string GetSystemVersionAsString();

// Returns the version of the operating system in double representation.
// Uses a cached value of the system version.
double GetSystemVersion();

// Returns the device type.
// Examples: ”iPhone” and ”iPod touch”.
std::string GetDeviceType();
#endif  // defined(RX_IOS)

// Returns a more detailed device name.
// Examples: "iPhone 5s (GSM)" and "iPhone 6 Plus".
std::string GetDeviceName();

// Returns the name of the process. Does not uniquely identify the process.
std::string GetProcessName();

// Returns the identifier of the process (often called process ID).
int GetProcessID();

// Returns a string containing the version of the operating system on which the
// process is executing. The string is string is human readable, localized, and
// is appropriate for displaying to the user.
std::string GetOSVersionString();

// Returns the number of processing cores available on the device.
int GetProcessorCount();

#if defined(FDKAAC_DEMO_IOS)
// Indicates whether Low Power Mode is enabled on the iOS device.
bool GetLowPowerModeEnabled();
#endif


#endif  // SDK_OBJC_NATIVE_SRC_AUDIO_HELPERS_H_
