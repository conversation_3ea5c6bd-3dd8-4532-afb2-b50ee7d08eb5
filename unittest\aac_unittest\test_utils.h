#ifndef TEST_UTILS_H
#define TEST_UTILS_H

#include "stdint.h"
#include "string"
#include "vector"
#include "random"

template<typename T>
T random_pick(std::vector<T>& vec) {
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<int> dist(0, vec.size() - 1);
 
    int index = dist(gen);
    return vec[index];
}

int av_clip_c(int a, int amin, int amax);

bool sleep_ms(int32_t milliseconds);

void insert_random_data(uint8_t *input_data, int input_size);

void insert_zero_data(uint8_t *input_data, int input_size);

std::string get_resource_dir(std::string dir_name = "");

std::string get_resource_gen_dir(std::string dir_name = "");

std::vector<std::string> list_all_files_with_extension(std::string dir, std::string file_extension);

void stereo_to_mono(const int16_t* src_audio, int samples_per_channel, int16_t* dst_audio);

void mono_to_stereo(const int16_t* src_audio, int samples_per_channel, int16_t* dst_audio);
struct AudioFrame {
    int16_t pcm_data[8192] = {0};
    int channels;
    int samples_per_channel;
};

struct AudioStream {
    uint8_t encoded_data[8192] = {0};
    int encoded_len;
};

#endif