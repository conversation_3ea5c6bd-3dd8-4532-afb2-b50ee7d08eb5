import os
import sys
import json
import shutil

from scripts.bytesh import JobStatus

import scripts.log as log
import scripts.bytesh as bytesh

default_repo_root_dir = os.path.abspath(os.path.join(os.path.dirname(os.path.abspath(__file__)), '../../../'))
repo_root_dir = os.environ.get('REPO_ROOT_DIR', default_repo_root_dir)
sys.path.append(os.path.join(repo_root_dir, 'build_system'))

from scripts.submodule_init_repo import submodule_download_decorator

def help():
    return 0

def get_cmake_cmds(**kwargs) -> list:
    cmds = [
        'cmake',
        '-G "Visual Studio 16 2019"',
        '-B{}'.format(kwargs['build_path']),
        '-A {}'.format(kwargs['arch']),
        '-DCMAKE_BUILD_TYPE={}'.format(kwargs['build_type']),
        '-DCMAKE_MSVC_RUNTIME_LIBRARY={}'.format(kwargs['crt']),
        '-DCMAKE_CXX_STANDARD=17',
        '-DBUILD_SHARED_LIBS=ON',
        '-DBUILD_PROGRAMS=OFF',
        '-DBUILD_ANALYSIS_DEMO=OFF',
        '-DCMAKE_VERBOSE_LINKER=ON',
        '-DFDK_AAC_INSTALL_CMAKE_CONFIG_MODULE=OFF',
        '-DFDK_AAC_INSTALL_PKGCONFIG_MODULE=OFF',
    ]
    cmds += [f'-D{key}={value}' for key, value in kwargs['extra_cmake_args'].items()]
    return cmds

# allow_list 下载指定submodule仓库
@submodule_download_decorator(repo_root_dir)
def do(config=None):
    log.info("start run action: {}".format(os.environ['BX_ACTION_NAME']))

    platform_name_list = ['win64', 'win32']
    build_type_list = ['Debug', 'Release']
    crt_list = ['MD', 'MT']

    for platform_name_item in platform_name_list:
        for build_type in build_type_list:
            for crt in crt_list:

                build_path = os.path.join(repo_root_dir, 'build_' + platform_name_item + build_type + crt)
                
                extra_cmake_args = json.loads(os.environ['CONFIGURATION_BX_EXTRA_CMAKE_ARGS']) if os.environ.get('CONFIGURATION_BX_EXTRA_CMAKE_ARGS', '') else {}
                
                if platform_name_item == "win64" :
                    arch="x64"
                elif platform_name_item == "win32" :
                    arch="Win32"

                if crt == "MD":
                    if build_type == "Release" :
                        CMAKE_MSVC_RUNTIME_LIBRARY="MultiThreadedDLL"
                    else:
                        CMAKE_MSVC_RUNTIME_LIBRARY="MultiThreadedDebugDLL"
                elif crt == "MT":
                    if build_type == "Release":
                        CMAKE_MSVC_RUNTIME_LIBRARY="MultiThreaded"
                    else:
                        CMAKE_MSVC_RUNTIME_LIBRARY="MultiThreadedDebug"

                cmake_cmds = get_cmake_cmds(build_path=build_path, arch = arch, build_type=build_type, crt=CMAKE_MSVC_RUNTIME_LIBRARY, extra_cmake_args=extra_cmake_args)
                log.info(' '.join(cmake_cmds))

                # generate msvc project
                os.chdir(repo_root_dir)
                ret = bytesh.do(cmake_cmds, env=os.environ)
                if ret != 0:
                    log.error('ERROR: generate msvc project failed')
                    return JobStatus.failed()

                # build target
                build_targets = ['fdk-aac',]
                os.chdir(build_path)
                for target in build_targets:
                    build_cmds = [
                        'cmake',
                        '--build {}'.format(build_path),
                        '--config {}'.format(build_type),
                        '--target {}'.format(target),
                    ]
                    ret = bytesh.do(build_cmds, env=os.environ)
                    if ret != 0:
                        log.error(f'ERROR: build target {target} failed')
                        return JobStatus.failed()

                if ret != 0:
                    log.error(f'AudioXAAC Windows UT failed')
                    return JobStatus.failed()
                
    log.print_succ(f'AudioXAAC Windows UT success')
    
    return JobStatus.success()
