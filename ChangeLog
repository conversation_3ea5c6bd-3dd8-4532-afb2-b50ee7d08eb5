0.1.5
 - Updated upstream sources
 - Fixed building with GCC 3.3 and 3.4
 - Fixed building with GCC 6
 - AArch64 optimizations
 - Make<PERSON>les for building with MSVC
 - Support building the code in C++11 mode

0.1.4
 - Updated upstream sources, with minor changes to the decoder API
   breaking the ABI. (Calling code using AUDIO_CHANNEL_TYPE may need to
   be updated. A new option AAC_PCM_LIMITER_ENABLE has been added, enabled
   by default, which incurs extra decoding delay.)
 - PowerPC optimizations, fixes for building on AIX
 - Support for reading streamed wav files in the encoder example
 - Fix VBR encoding of sample rates over 64 kHz

0.1.3
 - Updated upstream sources, with a number of crash fixes and new features
   (including support for encoding 7.1)

0.1.2
 - Fix a few more crashes
 - Include dependency libs (such as -lm) in the pkg-config file

0.1.1
 - Updated to a new upstream version from Android 4.2, fixing a lot of crashes
 - Cleanup of autotools usage
 - Make sure the shared library links to libm if necessary
 - Performance improvements on x86
 - Added support for WG4/DVD audio channel mappings
 - Minimized the differences to upstream
 - Added an example encoder tool

0.1.0
 - Initial release of fdk-aac
 - autotools based build system
 - Enable setting VBR bitrate modes
