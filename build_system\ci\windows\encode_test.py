import os
import sys
import json
import shutil

from scripts.bytesh import JobStatus

import scripts.log as log
import scripts.bytesh as bytesh

default_repo_root_dir = os.path.abspath(os.path.join(os.path.dirname(os.path.abspath(__file__)), '../../../'))
repo_root_dir = os.environ.get('REPO_ROOT_DIR', default_repo_root_dir)
sys.path.append(os.path.join(repo_root_dir, 'build_system'))

from scripts.submodule_init_repo import submodule_download_decorator

def help():
    return 0

def get_cmake_cmds(**kwargs) -> list:
    cmds = [
        'cmake',
        '-G "Visual Studio 16 2019"',
        '-B{}'.format(kwargs['build_path']),
        '-A x64',
        '-DFDK_AAC_BUILD_TEST=ON',
        '-DINSTALL_GTEST=OFF',
        '-DBUILD_GMOCK=OFF',
        '-DCMAKE_CXX_STANDARD=17',
        '-DBUILD_SHARED_LIBS=OFF',
        '-DCMAKE_BUILD_TYPE=Release',
        '-DBUILD_PROGRAMS=OFF',
    ]
    cmds += [f'-D{key}={value}' for key, value in kwargs['extra_cmake_args'].items()]
    return cmds


# allow_list 下载指定submodule仓库
@submodule_download_decorator(repo_root_dir)
def do(config=None):
    log.info("start run action: {}".format(os.environ['BX_ACTION_NAME']))

    build_path = os.path.join(repo_root_dir, 'build_win')
    build_type = 'Release'

    extra_cmake_args = json.loads(os.environ['CONFIGURATION_BX_EXTRA_CMAKE_ARGS']) if os.environ.get('CONFIGURATION_BX_EXTRA_CMAKE_ARGS', '') else {}
    
    cmake_cmds = get_cmake_cmds(build_path=build_path, build_type=build_type, extra_cmake_args=extra_cmake_args)
    log.info(' '.join(cmake_cmds))

    # generate msvc project
    os.chdir(repo_root_dir)
    ret = bytesh.do(cmake_cmds, env=os.environ)
    if ret != 0:
        log.error('ERROR: generate msvc project failed')
        return JobStatus.failed()

    # build target
    build_targets = ['aac_unittest',]
    os.chdir(build_path)
    for target in build_targets:
        build_cmds = [
            'cmake',
            '--build {}'.format(build_path),
            '--config {}'.format(build_type),
            '--target {}'.format(target),
        ]
        ret = bytesh.do(build_cmds, env=os.environ)
        if ret != 0:
            log.error(f'ERROR: build target {target} failed')
            return JobStatus.failed()

    release_path = os.path.join(build_path, 'Release')
    if not os.path.exists(release_path):
        log.error(f'ERROR: release path {release_path} not exists')
        return JobStatus.failed()
    os.chdir(release_path)
    ret = bytesh.do(['.\\aac_unittest.exe'], env=os.environ)

    if ret != 0:
        log.error(f'AudioXAAC Windows UT failed')
        return JobStatus.failed()
    
    log.print_succ(f'AudioXAAC Windows UT success')
    
    return JobStatus.success()
