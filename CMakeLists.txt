# # CMake project for AudioXAAC
#
# ## Basic usage
#
# 1. Download and install CMake >= 3.10 (CMake >= 3.15 is recommended)
# 2. Clone AudioXAAC repository
# 3. In AudioXAAC directory create and go to `build` subdirectory:
#
#    mkdir build
#    cd build
#
# 4. Run cmake to configure project with desired build type (Release|Debug):
#
#    cmake .. -DCMAKE_BUILD_TYPE=Release
#
#    Note CMake configuration without CMAKE_BUILD_TYPE option will not set build
#    type to some default and it is most likely is not what you want.
#
#    If you want to build static (default is shared library), add BUILD_SHARED_LIBS=OFF option:
#
#    #    cmake .. -DBUILD_SHARED_LIBS=OFF
#
#    You can combine options as well:
#
#    cmake .. -DCMAKE_BUILD_TYPE=Release -DBUILD_SHARED_LIBS=OFF
#
# 5. Run cmake to build project:
#
#    cmake --build . -c Release

cmake_minimum_required(VERSION 3.5.1)

# Policies

## Enables CMAKE_MSVC_RUNTIME_LIBRARY option support for CMake >= 3.15
## if you want to use a MSVC multi-threaded statically-linked runtime library
## If you enable it, <PERSON><PERSON><PERSON> will build fdk-acc.dll without external dependencies.
##
## Example usage:
##
## cmake .. -DCMAKE_MSVC_RUNTIME_LIBRARY="MultiThreaded$<$<CONFIG:Debug>:Debug>
if(POLICY CMP0091)
  cmake_policy(SET CMP0091 NEW)
endif()

project(AudioXAAC VERSION 0.1.5)

# Includes

include(CheckFunctionExists)
include(CheckLibraryExists)
include(GNUInstallDirs)
include(CMakePackageConfigHelpers)
include(FeatureSummary)

function(check_flag NAME FLAG)
  include(CheckCCompilerFlag)
  check_c_compiler_flag(${FLAG} ${NAME}_SUPPORTED)
endfunction()


if(CMAKE_VERSION VERSION_LESS 3.11)
  set(CPACK_PACKAGE_VERSION_MAJOR ${PROJECT_VERSION_MAJOR})
  set(CPACK_PACKAGE_VERSION_MINOR ${PROJECT_VERSION_MINOR})
  set(CPACK_PACKAGE_VERSION_PATCH ${PROJECT_VERSION_PATCH})
endif()
include(CPack)

# Options
set(AUDIOX_AAC_ROOT_DIR ${CMAKE_CURRENT_SOURCE_DIR} CACHE STRING "Root directory of AudioXAAC project")

set(AUDIOX_AAC_VERSION_MAJOR "1" CACHE STRING "major vsesion of AudioXAAC project")
set(AUDIOX_AAC_VERSION_MINOR "0" CACHE STRING "minor version of AudioXAAC project")
set(AUDIOX_AAC_VERSION_REVISION "4" CACHE STRING "revison version of AudioXAAC project")

option(BUILD_SHARED_LIBS "Build shared library" ON)
option(BUILD_PROGRAMS "Build aac-enc utility" ON)
option(FDK_AAC_INSTALL_CMAKE_CONFIG_MODULE "Install CMake package configuration file" ON)
option(FDK_AAC_INSTALL_PKGCONFIG_MODULE "Install pkg-config .pc file" ON)
option(FDK_AAC_BUILD_TEST "Build unittests" OFF)
option(FDK_AAC_BUILD_ALL_UT "Build all unittest cases" OFF)

if(APPLE)
  set(FDK_AAC_BUILD_FRAMEWORK_HELP_STR "build Framework bundle for Apple systems.")
  option(FDK_AAC_BUILD_FRAMEWORK ${FDK_AAC_BUILD_FRAMEWORK_HELP_STR} OFF)
  add_feature_info(FDK_AAC_BUILD_FRAMEWORK FDK_AAC_BUILD_FRAMEWORK ${FDK_AAC_BUILD_FRAMEWORK_HELP_STR})
endif()

if(FDK_AAC_BUILD_FRAMEWORK OR BUILD_SHARED_LIBS)
  set(BUILD_SHARED_LIBS ON)
endif()


# Checks
if(NOT MSVC)
  check_flag(HIDDEN_VISIBILITY -fvisibility=hidden)
endif()


## Check if math functions are in separate library (Most of Linux distros, maybe some other OSes)
check_function_exists(sin HAVE_DEFAULT_MATH)
if(NOT HAVE_DEFAULT_MATH)
  check_library_exists(m sin "" HAVE_LIBM)
endif()

# Library
feature_summary(WHAT ALL)

## Sources

FILE(GLOB AACDEC_SRC libAACdec/src/*.cpp)
FILE(GLOB AACENC_SRC libAACenc/src/*.cpp)
FILE(GLOB FDK_SRC libFDK/src/*.cpp)
FILE(GLOB MPEGTPDEC_SRC libMpegTPDec/src/*.cpp)
FILE(GLOB MPEGTPENC_SRC libMpegTPEnc/src/*.cpp)
FILE(GLOB PCMUTILS_SRC libPCMutils/src/*.cpp)
FILE(GLOB SBRDEC_SRC libSBRdec/src/*.cpp)
FILE(GLOB SBRENC_SRC libSBRenc/src/*.cpp)
FILE(GLOB SYS_SRC libSYS/src/*.cpp)

if(BUILD_PROGRAMS AND (${CMAKE_SYSTEM_NAME} STREQUAL "iOS"))
  FILE(GLOB IOS_HELPERS_SRC device_helpers/ios/*.mm)
endif()

set(fdk_aacinclude_HEADERS
  libSYS/include/machine_type.h
  libSYS/include/genericStds.h
  libSYS/include/FDK_audio.h
  libAACenc/include/aacenc_lib.h
  libAACdec/include/aacdecoder_lib.h)

set(libfdk_aac_SOURCES
    ${AACDEC_SRC} ${AACENC_SRC}
    ${MPEGTPDEC_SRC} ${MPEGTPENC_SRC}
    ${SBRDEC_SRC} ${SBRENC_SRC}
    ${PCMUTILS_SRC} ${FDK_SRC} ${SYS_SRC})

if(WIN32 AND BUILD_SHARED_LIBS)
  set(libfdk_aac_SOURCES ${libfdk_aac_SOURCES} fdk-aac.def)
endif()

## Create Library target. Actually fdk-aac is enough, but we also create
## FDK-AAC::fdk-aac for consistence with config-file package.

add_library(fdk-aac ${libfdk_aac_SOURCES} ${fdk_aacinclude_HEADERS})
add_library(FDK-AAC::fdk-aac ALIAS fdk-aac)


if(MSVC AND BUILD_SHARED_LIBS)
  configure_file(${AUDIOX_AAC_ROOT_DIR}/ld_script/windows/audioxaac.rc.cmake ${CMAKE_BINARY_DIR}/ld_script/windows/audioxaac.rc)
  target_sources(fdk-aac PRIVATE ${CMAKE_BINARY_DIR}/ld_script/windows/audioxaac.rc)

  # # Use windows signtool to sign dll
  # set(WINDOWS_SIGN_SCRIPT ${AUDIOX_AAC_ROOT_DIR}/build_system/tools/scripts/windows_sign_cert.py)
  # if (AUDIOX_AAC_ENABLE_WINDOWS_SIGN AND EXISTS ${WINDOWS_SIGN_SCRIPT} AND DEFINED ENV{WINDOWS_SIGN_TOOL} AND DEFINED ENV{WINDOWS_CERT_FILE})
  #     add_custom_command(
  #         TARGET fdk-aac
  #         POST_BUILD
  #         COMMAND ${Python3_EXECUTABLE} ${WINDOWS_SIGN_SCRIPT} ${RTC_WINDOWS_ARCHIVE_BIN_PATH} dll
  #     )
  # endif ()

endif()


## Hide symbols
if(BUILD_SHARED_LIBS)
  message(STATUS "AudioXAAC building shared libs.")
  if(WIN32)
    target_compile_definitions(fdk-aac PRIVATE DLL_EXPORT)
  elseif(HIDDEN_VISIBILITY_SUPPORTED)
    set_target_properties(fdk-aac PROPERTIES C_VISIBILITY_PRESET hidden)
    set_target_properties(fdk-aac PROPERTIES CXX_VISIBILITY_PRESET hidden)
  endif()
endif()


## Library target configuration

### Library target includes
target_include_directories(fdk-aac
  PUBLIC
    $<INSTALL_INTERFACE:${CMAKE_INSTALL_INCLUDEDIR}>
    $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/libAACdec/include>
    $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/libAACenc/include>
    $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/libSYS/include>
  PRIVATE
    libSBRdec/include
    libSBRenc/include
    libMpegTPDec/include
    libMpegTPEnc/include
    libFDK/include
    libPCMutils/include)

### Link math library if required
target_link_libraries(fdk-aac PRIVATE $<$<BOOL:${HAVE_LIBM}>:m>)

### Set public headers and shared library version. Version info is critical for Unix-like OSes.
set_target_properties(fdk-aac PROPERTIES
  PUBLIC_HEADER "${fdk_aacinclude_HEADERS}"
  VERSION "${AUDIOX_AAC_VERSION_MAJOR}.${AUDIOX_AAC_VERSION_MINOR}.${AUDIOX_AAC_VERSION_REVISION}"
  SOVERSION 2
  MACHO_COMPATIBILITY_VERSION 3.0.0
  MACHO_CURRENT_VERSION 3.1.0
  OUTPUT_NAME "AudioXAAC"
)

### Some compiler options from Makefile.am
if(MSVC)
  target_compile_options(fdk-aac PRIVATE /EHsc)
else()
  target_compile_options(fdk-aac PRIVATE -fno-exceptions -fno-rtti)
endif()

### Set proper name for MinGW or Cygwin DLL

if((MINGW OR CYGWIN) AND BUILD_SHARED_LIBS)
  set_property(TARGET fdk-aac PROPERTY RUNTIME_OUTPUT_NAME "AudioXAAC-2")
endif()

## Frameworks config
if(FDK_AAC_BUILD_FRAMEWORK)
  if(${CMAKE_SYSTEM_NAME} STREQUAL "iOS")
    set_target_properties(fdk-aac PROPERTIES
                          MACOSX_FRAMEWORK_INFO_PLIST "${PROJECT_SOURCE_DIR}/cmake/iosInfo.plist.in")
  else()
    set_target_properties(fdk-aac PROPERTIES
                          MACOSX_FRAMEWORK_INFO_PLIST "${PROJECT_SOURCE_DIR}/cmake/MacOSXBundleInfo.plist.in")
  endif()
  set_target_properties(fdk-aac PROPERTIES
                        FRAMEWORK TRUE
                        FRAMEWORK_VERSION ${PROJECT_VERSION}
                        MACOSX_FRAMEWORK_IDENTIFIER "com.bytedance.rtc.AudioXAAC"
                        XCODE_ATTRIBUTE_PRODUCT_BUNDLE_IDENTIFIER "com.bytedance.rtc.AudioXAAC"
                        MACOSX_FRAMEWORK_SHORT_VERSION_STRING ${PROJECT_VERSION}
                        MACOSX_FRAMEWORK_BUNDLE_VERSION ${PROJECT_VERSION}
                        XCODE_ATTRIBUTE_INSTALL_PATH "@rpath"
                        OUTPUT_NAME "AudioXAAC")
endif()

## Library installation

### Note we export `fdk-aac-targets` to use with config-file package.
install(TARGETS fdk-aac EXPORT fdk-aac-targets
  RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR} COMPONENT BIN
  ARCHIVE DESTINATION ${CMAKE_INSTALL_LIBDIR} COMPONENT DEV
  LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR} COMPONENT DEV
  FRAMEWORK DESTINATION ${CMAKE_INSTALL_PREFIX} COMPONENT DEV
  PUBLIC_HEADER DESTINATION ${CMAKE_INSTALL_INCLUDEDIR}/AudioXAAC COMPONENT DEV)

### Configure and install pkg-config module.
if(FDK_AAC_INSTALL_PKGCONFIG_MODULE)
  set(prefix ${CMAKE_INSTALL_PREFIX})
  set(exec_prefix "\$\{prefix\}")
  set(libdir "\$\{exec_prefix\}/${CMAKE_INSTALL_LIBDIR}")
  set(includedir "\$\{prefix\}/${CMAKE_INSTALL_INCLUDEDIR}")
  set(PACKAGE_VERSION ${PROJECT_VERSION})
  if(HAVE_LIBM)
    if(BUILD_SHARED_LIBS)
      set(LIBS_PRIVATE "-lm")
    else()
      set(LIBS_PUBLIC "-lm")
    endif()
  endif()
  configure_file(fdk-aac.pc.in fdk-aac.pc @ONLY)
  install(
    FILES
      ${CMAKE_CURRENT_BINARY_DIR}/fdk-aac.pc
    DESTINATION ${CMAKE_INSTALL_LIBDIR}/pkgconfig)
endif()

### Configure and install config-file package. Something like pkg-config module, but for Windows.
if(FDK_AAC_INSTALL_CMAKE_CONFIG_MODULE)

  set(CMAKE_INSTALL_PACKAGEDIR ${CMAKE_INSTALL_LIBDIR}/cmake/fdk-aac)

  configure_package_config_file(fdk-aac-config.cmake.in ${CMAKE_CURRENT_BINARY_DIR}/fdk-aac-config.cmake
    INSTALL_DESTINATION ${CMAKE_INSTALL_PACKAGEDIR})

  if(CMAKE_VERSION VERSION_LESS 3.11)
    write_basic_package_version_file(fdk-aac-config-version.cmake COMPATIBILITY SameMajorVersion)
  else()
    write_basic_package_version_file(fdk-aac-config-version.cmake COMPATIBILITY SameMinorVersion)
  endif()

  install(EXPORT fdk-aac-targets
    NAMESPACE FDK-AAC::
    DESTINATION ${CMAKE_INSTALL_PACKAGEDIR})
  install(
    FILES
      ${CMAKE_CURRENT_BINARY_DIR}/fdk-aac-config.cmake
      ${CMAKE_CURRENT_BINARY_DIR}/fdk-aac-config-version.cmake
    DESTINATION ${CMAKE_INSTALL_PACKAGEDIR})

endif()

# Programs

if(BUILD_PROGRAMS)

  ## fdkaac Program sources

  set(aac_enc_SOURCES
          aac-enc.cpp
          wavreader.c
          wavreader.h
          wavwriter.c
          wavwriter.h
          )

  ## Program target
  if(${CMAKE_SYSTEM_NAME} STREQUAL "iOS")
    add_executable(aac-enc ${aac_enc_SOURCES} ${IOS_HELPERS_SRC})
  else()
    add_executable(aac-enc ${aac_enc_SOURCES})
  endif()

  ## Program target configuration
  target_link_libraries(aac-enc PRIVATE fdk-aac)
  target_compile_definitions(aac-enc PRIVATE $<$<BOOL:${MSVC}>:_CRT_SECURE_NO_WARNINGS>)
  if(WIN32)
    target_sources(aac-enc PRIVATE win32/getopt.h)
    target_include_directories(aac-enc PRIVATE win32)
  endif()

  if(${CMAKE_SYSTEM_NAME} STREQUAL "iOS")
    message(STATUS "AudioXAAC building ios test programs.")
    target_compile_definitions(aac-enc PRIVATE FDKAAC_DEMO_IOS)
    set_target_properties(aac-enc PROPERTIES
      MACOSX_BUNDLE TRUE
      MACOSX_BUNDLE_INFO_PLIST "${PROJECT_SOURCE_DIR}/device_helpers/ios/Info.plist.in"
    )
  endif()

  if(NOT ${CMAKE_SYSTEM_NAME} STREQUAL "iOS")
    ## Program target installation
    install(TARGETS aac-enc RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR})
  endif()

  ## mediacodec aac demo
  if(APPLE) 
    set(atx_demo_SOURCES
        atx_aac_demo.cpp
        wavreader.c
        wavreader.h
        wavwriter.c
        wavwriter.h
        )

    ## Program target
    if(${CMAKE_SYSTEM_NAME} STREQUAL "iOS")
      add_executable(atx_aac_demo ${atx_demo_SOURCES} ${IOS_HELPERS_SRC})
    else()
      add_executable(atx_aac_demo ${atx_demo_SOURCES})
    endif()

    # find_library(found_framework AudioToolbox)
    target_link_libraries(atx_aac_demo PRIVATE -Wl,-framework,AudioToolbox)

    ## Program target configuration
    # target_link_libraries(aac-enc PRIVATE fdk-aac)
    target_compile_definitions(atx_aac_demo PRIVATE $<$<BOOL:${MSVC}>:_CRT_SECURE_NO_WARNINGS>)
    # if(WIN32)
    #   target_sources(aac-enc PRIVATE win32/getopt.h)
    #   target_include_directories(aac-enc PRIVATE win32)
    # endif()

    if(${CMAKE_SYSTEM_NAME} STREQUAL "iOS")
      message(STATUS "mediacodec building ios test programs.")
      target_compile_definitions(atx_aac_demo PRIVATE FDKAAC_DEMO_IOS)
      set_target_properties(atx_aac_demo PROPERTIES
        MACOSX_BUNDLE TRUE
        MACOSX_BUNDLE_INFO_PLIST "${PROJECT_SOURCE_DIR}/device_helpers/ios/Info.plist.in"
      )
    endif()

    if(NOT ${CMAKE_SYSTEM_NAME} STREQUAL "iOS")
      ## Program target installation
      install(TARGETS atx_aac_demo RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR})
    endif()
  endif()
endif()


if(FDK_AAC_BUILD_TEST)
  ## build unittests
  add_subdirectory(${CMAKE_CURRENT_SOURCE_DIR}/unittest/googletest)
  add_subdirectory(${CMAKE_CURRENT_SOURCE_DIR}/unittest/push_resampler)
  set(
    unittest_sources
    ${CMAKE_CURRENT_SOURCE_DIR}/unittest/aac_unittest/test_main.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/unittest/aac_unittest/test_encode_transcode.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/unittest/aac_unittest/test_decode_transcode.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/unittest/aac_unittest/encoder_impl.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/unittest/aac_unittest/decoder_impl.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/unittest/aac_unittest/test_utils.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/wavreader.c
    ${CMAKE_CURRENT_SOURCE_DIR}/wavreader.h
    ${CMAKE_CURRENT_SOURCE_DIR}/wavwriter.c
    ${CMAKE_CURRENT_SOURCE_DIR}/wavwriter.h
  )
  add_executable(aac_unittest ${unittest_sources})
  target_include_directories(aac_unittest PRIVATE ${gtest_SOURCE_DIR}/include)
  target_include_directories(aac_unittest PRIVATE  ${CMAKE_CURRENT_SOURCE_DIR}/unittest/push_resampler/inc)
  target_include_directories(aac_unittest PRIVATE  ${CMAKE_CURRENT_SOURCE_DIR}/libAnalysis/include)
  target_include_directories(aac_unittest PRIVATE libAACenc/include)
  target_include_directories(aac_unittest PRIVATE libAACdec/include)
#  target_include_directories(aac_unittest PRIVATE libSYS/include)
  target_link_libraries(aac_unittest PRIVATE gtest gtest_main)
  target_link_libraries(aac_unittest PRIVATE fdk-aac)
  target_link_libraries(aac_unittest PRIVATE push_resampler)
  target_compile_definitions(aac_unittest PRIVATE FDK_BASE_DIR_PATH="${CMAKE_CURRENT_SOURCE_DIR}")
  if(FDK_AAC_BUILD_ALL_UT)
    target_compile_definitions(aac_unittest PRIVATE BUILD_ALL_UT)
  endif()
  if(CMAKE_SYSTEM_NAME MATCHES "Linux")
    target_link_libraries(aac_unittest PRIVATE "stdc++fs")
  endif()
endif()
