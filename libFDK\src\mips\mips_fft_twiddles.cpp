
/* -----------------------------------------------------------------------------------------------------------
Software License for The Fraunhofer FDK AAC Codec Library for Android

© Copyright  1995 - 2013 Fraunhofer-Gesellschaft zur Förderung der angewandten Forschung e.V.
  All rights reserved.

 1.    INTRODUCTION
The Fraunhofer FDK AAC Codec Library for Android ("FDK AAC Codec") is software that implements
the MPEG Advanced Audio Coding ("AAC") encoding and decoding scheme for digital audio.
This FDK AAC Codec software is intended to be used on a wide variety of Android devices.

AAC's HE-AAC and HE-AAC v2 versions are regarded as today's most efficient general perceptual
audio codecs. AAC-ELD is considered the best-performing full-bandwidth communications codec by
independent studies and is widely deployed. AAC has been standardized by ISO and IEC as part
of the MPEG specifications.

Patent licenses for necessary patent claims for the FDK AAC Codec (including those of <PERSON><PERSON><PERSON><PERSON><PERSON>)
may be obtained through Via Licensing (www.vialicensing.com) or through the respective patent owners
individually for the purpose of encoding or decoding bit streams in products that are compliant with
the ISO/IEC MPEG audio standards. Please note that most manufacturers of Android devices already license
these patent claims through Via Licensing or directly from the patent owners, and therefore FDK AAC Codec
software may already be covered under those patent licenses when it is used for those licensed purposes only.

Commercially-licensed AAC software libraries, including floating-point versions with enhanced sound quality,
are also available from Fraunhofer. Users are encouraged to check the Fraunhofer website for additional
applications information and documentation.

2.    COPYRIGHT LICENSE

Redistribution and use in source and binary forms, with or without modification, are permitted without
payment of copyright license fees provided that you satisfy the following conditions:

You must retain the complete text of this software license in redistributions of the FDK AAC Codec or
your modifications thereto in source code form.

You must retain the complete text of this software license in the documentation and/or other materials
provided with redistributions of the FDK AAC Codec or your modifications thereto in binary form.
You must make available free of charge copies of the complete source code of the FDK AAC Codec and your
modifications thereto to recipients of copies in binary form.

The name of Fraunhofer may not be used to endorse or promote products derived from this library without
prior written permission.

You may not charge copyright license fees for anyone to use, copy or distribute the FDK AAC Codec
software or your modifications thereto.

Your modified versions of the FDK AAC Codec must carry prominent notices stating that you changed the software
and the date of any change. For modified versions of the FDK AAC Codec, the term
"Fraunhofer FDK AAC Codec Library for Android" must be replaced by the term
"Third-Party Modified Version of the Fraunhofer FDK AAC Codec Library for Android."

3.    NO PATENT LICENSE

NO EXPRESS OR IMPLIED LICENSES TO ANY PATENT CLAIMS, including without limitation the patents of Fraunhofer,
ARE GRANTED BY THIS SOFTWARE LICENSE. Fraunhofer provides no warranty of patent non-infringement with
respect to this software.

You may use this FDK AAC Codec software or modifications thereto only for purposes that are authorized
by appropriate patent licenses.

4.    DISCLAIMER

This FDK AAC Codec software is provided by Fraunhofer on behalf of the copyright holders and contributors
"AS IS" and WITHOUT ANY EXPRESS OR IMPLIED WARRANTIES, including but not limited to the implied warranties
of merchantability and fitness for a particular purpose. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR
CONTRIBUTORS BE LIABLE for any direct, indirect, incidental, special, exemplary, or consequential damages,
including but not limited to procurement of substitute goods or services; loss of use, data, or profits,
or business interruption, however caused and on any theory of liability, whether in contract, strict
liability, or tort (including negligence), arising in any way out of the use of this software, even if
advised of the possibility of such damage.

5.    CONTACT INFORMATION

Fraunhofer Institute for Integrated Circuits IIS
Attention: Audio and Multimedia Departments - FDK AAC LL
Am Wolfsmantel 33
91058 Erlangen, Germany

www.iis.fraunhofer.de/amm
<EMAIL>
----------------------------------------------------------------------------------------------------------- */

/***************************  Fraunhofer IIS FDK Tools  **********************

******************************************************************************/

static const INT __twiddles_mips_fft32_16[] =
{
(0x7FFFFFFF), (0x00000000), (0x7641AF32), (0xCF043A9E), (0x5A827978), (0xA57D8646), (0x30FBC547), (0x89BE50C2), (0xFFFFFFA3), (0x80000002), (0xCF043AF8), (0x89BE50A8), (0xA57D865E), (0xA57D8670), (0x89BE5100), (0xCF043A24), (0x00000000), (0x00000000),
(0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000)
};

static const INT __twiddles_mips_fft32_32[] =
{
(0x7FFFFFFF), (0x00000000), (0x7D8A5F3C), (0xE70747B9), (0x7641AF32), (0xCF043A9E), (0x6A6D98A1), (0xB8E31318), (0x5A827978), (0xA57D8646), (0x471CED05), (0x95926772), (0x30FBC547), (0x89BE50C2), (0x18F8B888), (0x8275A0D1), (0xFFFFFFA3), (0x80000002),
(0xE70747BB), (0x8275A0C3), (0xCF043AF8), (0x89BE50A8), (0xB8E3139E), (0x95926705), (0xA57D865E), (0xA57D8670), (0x959266F7), (0xB8E313B4), (0x89BE5100), (0xCF043A24), (0x8275A0BE), (0xE70747D4), (0x00000000), (0x00000000), (0x00000000), (0x00000000),
(0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x37000000), (0x000080A3),
(0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000)
};

static const INT __twiddles_mips_fft32_64[] =
{
(0x7FFFFFFF), (0x00000000), (0x7F62368D), (0xF3742C9D), (0x7D8A5F3C), (0xE70747B9), (0x7A7D0559), (0xDAD7F3A2), (0x7641AF32), (0xCF043A9E), (0x70E2CBCD), (0xC3A945A1), (0x6A6D98A1), (0xB8E31318), (0x62F201C4), (0xAECC338B), (0x5A827978), (0xA57D8646),
(0x5133CC8F), (0x9D0DFE52), (0x471CED05), (0x95926772), (0x3C56BAB5), (0x8F1D3461), (0x30FBC547), (0x89BE50C2), (0x25280C05), (0x8582FA8C), (0x18F8B888), (0x8275A0D1), (0x0C8BD356), (0x809DC971), (0xFFFFFFA3), (0x80000002), (0xF3742CEE), (0x809DC96B),
(0xE70747BB), (0x8275A0C3), (0xDAD7F348), (0x8582FAC2), (0xCF043AF8), (0x89BE50A8), (0xC3A94669), (0x8F1D33C8), (0xB8E3139E), (0x95926705), (0xAECC33A5), (0x9D0DFE27), (0xA57D865E), (0xA57D8670), (0x9D0DFE16), (0xAECC33B9), (0x959266F7), (0xB8E313B4),
(0x8F1D34AD), (0xC3A944BC), (0x89BE5100), (0xCF043A24), (0x8582FABB), (0xDAD7F360), (0x8275A0BE), (0xE70747D4), (0x809DC968), (0xF3742D08), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000),
(0x00000000), (0x00000000), (0xFF7B0000), (0x2E8050F1), (0x10214482), (0x1BA00005), (0x0000C0FF), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000),
(0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000),
(0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000),
(0x00000000), (0x00000000)
};

static const INT __twiddles_mips_fft32_128[] =
{
(0x7FFFFFFF), (0x00000000), (0x7FD8878C), (0xF9B82681), (0x7F62368D), (0xF3742C9D), (0x7E9D55FB), (0xED37EF91), (0x7D8A5F3C), (0xE70747B9), (0x7C29FBEF), (0xE0E6068F), (0x7A7D0559), (0xDAD7F3A2), (0x78848419), (0xD4E0CB28), (0x7641AF32), (0xCF043A9E),
(0x73B5EBCF), (0xC945DFEB), (0x70E2CBCD), (0xC3A945A1), (0x6DCA0D27), (0xBE31E1BF), (0x6A6D98A1), (0xB8E31318), (0x66CF8103), (0xB3C01FE9), (0x62F201C4), (0xAECC338B), (0x5ED77C86), (0xAA0A5B2C), (0x5A827978), (0xA57D8646), (0x55F5A4ED), (0xA1288391),
(0x5133CC8F), (0x9D0DFE52), (0x4C3FDFCC), (0x99307EC5), (0x471CED05), (0x95926772), (0x41CE1ECD), (0x9235F32C), (0x3C56BAB5), (0x8F1D3461), (0x36BA2034), (0x8C4A1440), (0x30FBC547), (0x89BE50C2), (0x2B1F34BC), (0x877B7BDD), (0x25280C05), (0x8582FA8C),
(0x1F19F9F0), (0x83D60431), (0x18F8B888), (0x8275A0D1), (0x12C81090), (0x8162AA0A), (0x0C8BD356), (0x809DC971), (0x0647D949), (0x80277871), (0xFFFFFFA3), (0x80000002), (0xF9B826FB), (0x8027786E), (0xF3742CEE), (0x809DC96B), (0xED37EFB3), (0x8162AA00),
(0xE70747BB), (0x8275A0C3), (0xE0E60653), (0x83D60420), (0xDAD7F348), (0x8582FAC2), (0xD4E0CB84), (0x877B7BC6), (0xCF043AF8), (0x89BE50A8), (0xC945E00A), (0x8C4A1423), (0xC3A94669), (0x8F1D33C8), (0xBE31E16E), (0x9235F309), (0xB8E3139E), (0x95926705),
(0xB3C01F9D), (0x99307F35), (0xAECC33A5), (0x9D0DFE27), (0xAA0A5A88), (0xA128840F), (0xA57D865E), (0xA57D8670), (0xA12883FE), (0xAA0A5A9B), (0x9D0DFE16), (0xAECC33B9), (0x99307F26), (0xB3C01FB2), (0x959266F7), (0xB8E313B4), (0x9235F2FC), (0xBE31E184),
(0x8F1D34AD), (0xC3A944BC), (0x8C4A1418), (0xC945E021), (0x89BE5100), (0xCF043A24), (0x877B7BBD), (0xD4E0CB9C), (0x8582FABB), (0xDAD7F360), (0x83D603DC), (0xE0E60764), (0x8275A0BE), (0xE70747D4), (0x8162AA22), (0xED37EECF), (0x809DC968), (0xF3742D08),
(0x80277879), (0xF9B82615), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000),
(0x00000000), (0xA4370000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000),
(0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0xFF770000), (0x2E8050F1), (0x1A214462), (0x04D6D583), (0x40031282), (0x6469A735), (0x0B4C99A3), (0x0AF88594), (0xF90969D7), (0x96131C92), (0x025EEA10), (0x3A1FE421), (0x614FF390),
(0x1CFDC327), (0xC177E04F), (0xF4D87E82), (0x78253F39), (0xBB839F94), (0x998B3EB1), (0x0CBCC021), (0x41BEC843), (0xAC0EE121), (0x52719643), (0x909A2B1D), (0xF38931E1), (0xF41327FF), (0xF6099847), (0xA70D219C), (0x7BD52135), (0x78060F71), (0xEA3C87D8),
(0x3FAF3A24), (0xE4B2C421), (0xB99D1453), (0x2741E264), (0x2239813A), (0x2944DA20), (0x441EF7C4), (0x0BD0B720), (0xED84EA26), (0x73E0C0D2), (0x678E3039), (0x21420109), (0x8607492E), (0x28CEF440), (0x022768C8), (0xF68E3611), (0x84E84D55), (0x73004C04),
(0xF9B6630E), (0x677FFA8F), (0x530CB18F), (0x2C4EE310), (0xABC7537C), (0x82CFDBCB), (0x100C63A2), (0x876D75BA), (0xC683F888), (0x0CDC1E1E), (0xA600E833), (0x33036066), (0x4305049C), (0x40890713), (0x9D12532E), (0x7B6E2FE2), (0xE244CC09), (0x9FFB2082),
(0xAB3735D3), (0x00000080), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000),
(0x00000000), (0x00000000), (0x00000000), (0x00000000)
};


static const INT __twiddles_mips_fft32_256[] =
{
(0x7FFFFFFF), (0x00000000), (0x7FF62181), (0xFCDBD540), (0x7FD8878C), (0xF9B82681), (0x7FA736B2), (0xF6956FB7), (0x7F62368D), (0xF3742C9D), (0x7F0991C3), (0xF054D8DA), (0x7E9D55FB), (0xED37EF91), (0x7E1D93EA), (0xEA1DEBC6), (0x7D8A5F3C), (0xE70747B9),
(0x7CE3CEB0), (0xE3F47D95), (0x7C29FBEF), (0xE0E6068F), (0x7B5D03A1), (0xDDDC5B4F), (0x7A7D0559), (0xDAD7F3A2), (0x798A23A8), (0xD7D946C3), (0x78848419), (0xD4E0CB28), (0x776C4ED9), (0xD1EEF59D), (0x7641AF32), (0xCF043A9E), (0x7504D34C), (0xCC210D8B),
(0x73B5EBCF), (0xC945DFEB), (0x72552C79), (0xC67322B9), (0x70E2CBCD), (0xC3A945A1), (0x6F5F02CF), (0xC0E8B67E), (0x6DCA0D27), (0xBE31E1BF), (0x6C242969), (0xBB8532C0), (0x6A6D98A1), (0xB8E31318), (0x68A69E72), (0xB64BEAB9), (0x66CF8103), (0xB3C01FE9),
(0x64E8894A), (0xB140178C), (0x62F201C4), (0xAECC338B), (0x60EC383A), (0xAC64D51F), (0x5ED77C86), (0xAA0A5B2C), (0x5CB420CD), (0xA7BD229A), (0x5A827978), (0xA57D8646), (0x5842DD7E), (0xA34BDF4B), (0x55F5A4ED), (0xA1288391), (0x539B2AFB), (0x9F13C7DC),
(0x5133CC8F), (0x9D0DFE52), (0x4EBFE88F), (0x9B1776CB), (0x4C3FDFCC), (0x99307EC5), (0x49B41562), (0x975961A2), (0x471CED05), (0x95926772), (0x447ACD5D), (0x93DBD6AA), (0x41CE1ECD), (0x9235F32C), (0x3F17499F), (0x90A0FD42), (0x3C56BAB5), (0x8F1D3461),
(0x398CDCF3), (0x8DAAD35D), (0x36BA2034), (0x8C4A1440), (0x33DEF21F), (0x8AFB2C8E), (0x30FBC547), (0x89BE50C2), (0x2E110ABF), (0x8893B14A), (0x2B1F34BC), (0x877B7BDD), (0x2826B95E), (0x8675DC62), (0x25280C05), (0x8582FA8C), (0x2223A4D2), (0x84A2FC68),
(0x1F19F9F0), (0x83D60431), (0x1C0B824E), (0x831C314A), (0x18F8B888), (0x8275A0D1), (0x15E213FD), (0x81E26C0B), (0x12C81090), (0x8162AA0A), (0x0FAB26B9), (0x80F66E30), (0x0C8BD356), (0x809DC971), (0x096A90AB), (0x8058C955), (0x0647D949), (0x80277871),
(0x03242AF6), (0x8009DE81), (0xFFFFFFA3), (0x80000002), (0xFCDBD54E), (0x8009DE7F), (0xF9B826FB), (0x8027786E), (0xF6956F99), (0x8058C950), (0xF3742CEE), (0x809DC96B), (0xF054D88D), (0x80F66E47), (0xED37EFB3), (0x8162AA00), (0xEA1DEB4A), (0x81E26C2C),
(0xE70747BB), (0x8275A0C3), (0xE3F47DF5), (0x831C313B), (0xE0E60653), (0x83D60420), (0xDDDC5B6F), (0x84A2FC55), (0xDAD7F348), (0x8582FAC2), (0xD7D946E3), (0x8675DC4D), (0xD4E0CB84), (0x877B7BC6), (0xD1EEF581), (0x8893B132), (0xCF043AF8), (0x89BE50A8),
(0xCC210D35), (0x8AFB2CDB), (0xC945E00A), (0x8C4A1423), (0xC6732265), (0x8DAAD3B2), (0xC3A94669), (0x8F1D33C8), (0xC0E8B69C), (0x90A0FD21), (0xBE31E16E), (0x9235F309), (0xBB853205), (0x93DBD70E), (0xB8E3139E), (0x95926705), (0xB64BEAD5), (0x9759617B),
(0xB3C01F9D), (0x99307F35), (0xB140180C), (0x9B177652), (0xAECC33A5), (0x9D0DFE27), (0xAC64D4D8), (0x9F13C803), (0xAA0A5A88), (0xA128840F), (0xA7BD2310), (0xA34BDEC3), (0xA57D865E), (0xA57D8670), (0xA34BDEB2), (0xA7BD2322), (0xA12883FE), (0xAA0A5A9B),
(0x9F13C7F2), (0xAC64D4EB), (0x9D0DFE16), (0xAECC33B9), (0x9B177642), (0xB1401820), (0x99307F26), (0xB3C01FB2), (0x9759616C), (0xB64BEAEA), (0x959266F7), (0xB8E313B4), (0x93DBD700), (0xBB85321A), (0x9235F2FC), (0xBE31E184), (0x90A0FD14), (0xC0E8B6B2),
(0x8F1D34AD), (0xC3A944BC), (0x8DAAD3A6), (0xC673227C), (0x8C4A1418), (0xC945E021), (0x8AFB2C68), (0xCC210E37), (0x89BE5100), (0xCF043A24), (0x8893B128), (0xD1EEF599), (0x877B7BBD), (0xD4E0CB9C), (0x8675DC95), (0xD7D94608), (0x8582FABB), (0xDAD7F360),
(0x84A2FC4F), (0xDDDC5B88), (0x83D603DC), (0xE0E60764), (0x831C316D), (0xE3F47D14), (0x8275A0BE), (0xE70747D4), (0x81E26BFB), (0xEA1DEC5F), (0x8162AA22), (0xED37EECF), (0x80F66E44), (0xF054D8A6), (0x809DC968), (0xF3742D08), (0x8058C93B), (0xF69570B2),
(0x80277879), (0xF9B82615), (0x8009DE7E), (0xFCDBD568), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0xF1FF7A00), (0xE23A8050), (0x841A2114), (0xC588DA75), (0x02400143), (0xAED6AAD7), (0x94C2545B), (0x3E46156C), (0x05BFDF27), (0xB0822075),
(0xD24C7BC4), (0x62122054), (0x8CC41C49), (0x2C414972), (0x718D7C5E), (0xDEE2A3D8), (0x4544C135), (0x64292573), (0x76B51F8A), (0x4B7D6557), (0x87164907), (0xF59DBFEC), (0x04CEE9DE), (0xB4BC4C90), (0x9AFCCD30), (0x8C7A316F), (0x90FDACA7), (0x4E0D6383),
(0xAEF16C0B), (0x0622020A), (0x37B6A738), (0xFFBB7D71), (0x5FEDD12F), (0xEE47FDC8), (0x3DC7913F), (0xEAEEF7FD), (0x219DADEC), (0xF9E53F5F), (0xB3D82D9E), (0xBBB79BCB), (0x43AD2AAB), (0x0E7ADBC0), (0xABBD0952), (0x1A6EF43A), (0xCD9B2A9D), (0x7222B366),
(0x979E89E9), (0x02C9113F), (0x1D511466), (0x57A92206), (0x4A412075), (0xFD31D783), (0xA7542EFC), (0x24B76975), (0xB99962C7), (0x19F1D356), (0x9D12C3CB), (0x6D37D542), (0x29E7214D), (0x8900F645), (0xB154AD4A), (0xF047D988), (0x3F2E5E77), (0xAFB68C49),
(0xC9F19E51), (0x6192F53A), (0x5AE3D080), (0x1DA6C677), (0xD485D50C), (0x9AA7E8B2), (0xF55EE1D5), (0xC00B6932), (0x85ACE912), (0x8208B1B9), (0x80E14170), (0xA1D67C3F), (0x058035B3), (0xA4FE36CA), (0xBEE547E4), (0xEF276052), (0x42AA4388), (0x9C11A2ED),
(0x10202541), (0x2D480910), (0xB09B1AC4), (0x19E7D17D), (0x52082450), (0x3EED1705), (0xF20FF8A1), (0x89F6E17F), (0x17D7DC00), (0x09B9F2C3), (0x6378968C), (0xAF0607F6), (0xFDFDE0C9), (0x3CE3DFEE), (0x5168229E), (0x7CF79734), (0xF5FFF56F), (0x30700093),
(0x135F5C75), (0xE6D73EEE), (0x6E400DF9), (0x58252ACB), (0x557311CA), (0x5539B303), (0x56557355), (0xB6BDACEF), (0x59F9428D), (0x9AE63020), (0x558FF7E7), (0x7955806D), (0x09D549F3), (0x603BF5B7), (0x3134413B), (0xBCA7C1AA), (0xA98D1339), (0x57B29C97),
(0x50F1FF07), (0xE4A13980), (0xD0881A21), (0xC1FFE30A), (0xB0A66C19), (0x18416490), (0x160E0214), (0xA584CBB5), (0x02AAA82A), (0x73981340), (0x964A24DE), (0x6E829FC4), (0xDFD62360), (0x4C3BF152), (0x38721574), (0xD46BC912), (0x1EB7FDD8), (0x1CA6CBC0),
(0xBBCBC111), (0xA6FD1403), (0x76C688D9), (0xB47186CB), (0x5E98936D), (0x9BBF7E2E), (0x8B7FBCEC), (0x9DAF7EF5), (0x7E3A79F1), (0x9C896ACD), (0x628F249A), (0xDD6E51AF), (0xAF73994B), (0x5966995D), (0xB7620597), (0x369B1E54), (0x7CEDC787), (0xE7B15B1F),
(0xB36F5F7D), (0xB5B73EF6), (0xD76EEEE2), (0x14E86186), (0xCC159291), (0x4EC85CA3), (0xEC3C704E), (0x0B6633B3), (0x4D992D4B), (0xEB1B5774), (0xDFB9D508), (0x11783719), (0x77BB10B0), (0xFCDF8350), (0xD34A7C65), (0xEF6E57BB), (0xECD56C68), (0x8EB7CA36),
(0x2F26B136), (0x55631269), (0xEF1213E1), (0xA56AB844), (0xAA4B1D64), (0x64341EA9), (0x0CE9DB48), (0x460C2144), (0xADC50205), (0x515C472F), (0x304C29BD), (0x76AF8711), (0x557CADB7), (0x2A9BBC8E), (0xE50E145B), (0x9C2031FD), (0xD9121A8C), (0xF51A96FD),
(0x45CCBEBF), (0x5F884871), (0x66DAE291), (0x95ADC9E5), (0x33CF304D), (0x23C87DCE), (0xA7014FB3), (0xFB351E71), (0x8D60F66F), (0xBFD6DDD5), (0x151FE6F1), (0xDCC7935A), (0x0CACFBF0), (0xEE55A371), (0xBE46B755), (0x0F79DCD2), (0x23DFF76D), (0x83DE76D7),
(0x8B0E3AEE), (0xB01BFB44), (0x4E3619FA), (0xA19F9E64), (0xBD492AD9), (0x0899E45F), (0x8156E41D), (0x5DA539BA), (0x0AF35B2C), (0xDB89BAAE), (0xA128C966), (0xA02CEAEE), (0xC5A1B291), (0x08CBA93E), (0x90FD887A), (0x60304386), (0x6A59F1EA), (0xA461E67B),
(0xC1622B76), (0x6FA2F906), (0xAA3A401A), (0xB68EA47B), (0xC617A889), (0x6024A57B), (0x2DD53555), (0xF1FFEA30), (0x813A8050), (0x8D1A21B4), (0xFFE72CF7), (0xA37415E1), (0x30A2C830), (0xA3A1CA28), (0x4C5E3586), (0x309861B9), (0x1F49850D), (0x6164C748),
(0x9D7048E5), (0xC124569E), (0xCCB0E111), (0x15B2BB86), (0x18371D9C), (0xB2ABD94D), (0x8EC656B3), (0xBF010995)
};



static const INT __twiddles_mips_fft32_512[] =
{
(0x7FFFFFFF), (0x00000000), (0x7FFD8859), (0xFE6DE2E0), (0x7FF62181), (0xFCDBD540), (0x7FE9CBBE), (0xFB49E6A3), (0x7FD8878C), (0xF9B82681), (0x7FC25595), (0xF826A464), (0x7FA736B2), (0xF6956FB7), (0x7F872BF2), (0xF5049800), (0x7F62368D), (0xF3742C9D),
(0x7F3857F4), (0xF1E43D1C), (0x7F0991C3), (0xF054D8DA), (0x7ED5E5C6), (0xEEC60F3C), (0x7E9D55FB), (0xED37EF91), (0x7E5FE490), (0xEBAA8944), (0x7E1D93EA), (0xEA1DEBC6), (0x7DD6668D), (0xE8922621), (0x7D8A5F3C), (0xE70747B9), (0x7D3980ED), (0xE57D5FE4),
(0x7CE3CEB0), (0xE3F47D95), (0x7C894BDA), (0xE26CB010), (0x7C29FBEF), (0xE0E6068F), (0x7BC5E296), (0xDF609002), (0x7B5D03A1), (0xDDDC5B4F), (0x7AEF6325), (0xDC59778B), (0x7A7D0559), (0xDAD7F3A2), (0x7A05EEA8), (0xD957DE6F), (0x798A23A8), (0xD7D946C3),
(0x7909A935), (0xD65C3B98), (0x78848419), (0xD4E0CB28), (0x77FAB98A), (0xD367044F), (0x776C4ED9), (0xD1EEF59D), (0x76D94983), (0xD078AD93), (0x7641AF32), (0xCF043A9E), (0x75A585D9), (0xCD91AB55), (0x7504D34C), (0xCC210D8B), (0x745F9DD3), (0xCAB26FB2),
(0x73B5EBCF), (0xC945DFEB), (0x7307C3C9), (0xC7DB6C45), (0x72552C79), (0xC67322B9), (0x719E2CDE), (0xC50D1164), (0x70E2CBCD), (0xC3A945A1), (0x7023109C), (0xC247CD62), (0x6F5F02CF), (0xC0E8B67E), (0x6E96A995), (0xBF8C0DD8), (0x6DCA0D27), (0xBE31E1BF),
(0x6CF934E8), (0xBCDA3EAE), (0x6C242969), (0xBB8532C0), (0x6B4AF258), (0xBA32CA42), (0x6A6D98A1), (0xB8E31318), (0x698C2487), (0xB79619C6), (0x68A69E72), (0xB64BEAB9), (0x67BD0FCC), (0xB5049380), (0x66CF8103), (0xB3C01FE9), (0x65DDFBD6), (0xB27E9D43),
(0x64E8894A), (0xB140178C), (0x63EF3286), (0xB0049AA9), (0x62F201C4), (0xAECC338B), (0x61F10027), (0xAD96ED77), (0x60EC383A), (0xAC64D51F), (0x5FE3B366), (0xAB35F58C), (0x5ED77C86), (0xAA0A5B2C), (0x5DC79D9C), (0xA8E2112C), (0x5CB420CD), (0xA7BD229A),
(0x5B9D1166), (0xA69B9B7D), (0x5A827978), (0xA57D8646), (0x5964649B), (0xA462EEB2), (0x5842DD7E), (0xA34BDF4B), (0x571DEEED), (0xA238627B), (0x55F5A4ED), (0xA1288391), (0x54CA0A2E), (0xA01C4C5C), (0x539B2AFB), (0x9F13C7DC), (0x52691241), (0x9E0EFF9D),
(0x5133CC8F), (0x9D0DFE52), (0x4FFB6572), (0x9C10CD90), (0x4EBFE88F), (0x9B1776CB), (0x4D8162D8), (0x9A22043F), (0x4C3FDFCC), (0x99307EC5), (0x4AFB6C9B), (0x9842F048), (0x49B41562), (0x975961A2), (0x4869E657), (0x9673DB8C), (0x471CED05), (0x95926772),
(0x45CD356F), (0x94B50D74), (0x447ACD5D), (0x93DBD6AA), (0x4325C102), (0x9306CAE7), (0x41CE1ECD), (0x9235F32C), (0x4073F245), (0x9169567D), (0x3F17499F), (0x90A0FD42), (0x3DB8324C), (0x8FDCEF37), (0x3C56BAB5), (0x8F1D3461), (0x3AF2EEBA), (0x8E61D331),
(0x398CDCF3), (0x8DAAD35D), (0x38249413), (0x8CF83C62), (0x36BA2034), (0x8C4A1440), (0x354D9033), (0x8BA06221), (0x33DEF21F), (0x8AFB2C8E), (0x326E5505), (0x8A5A7A4D), (0x30FBC547), (0x89BE50C2), (0x2F875216), (0x8926B65A), (0x2E110ABF), (0x8893B14A),
(0x2C98FBD1), (0x88054682), (0x2B1F34BC), (0x877B7BDD), (0x29A3C40F), (0x86F656AC), (0x2826B95E), (0x8675DC62), (0x26A82174), (0x85FA114F), (0x25280C05), (0x8582FA8C), (0x23A688D3), (0x85109CF7), (0x2223A4D2), (0x84A2FC68), (0x209F6FE1), (0x843A1D62),
(0x1F19F9F0), (0x83D60431), (0x1D935011), (0x8376B42E), (0x1C0B824E), (0x831C314A), (0x1A829FC0), (0x82C67F00), (0x18F8B888), (0x8275A0D1), (0x176DD9E1), (0x82299974), (0x15E213FD), (0x81E26C0B), (0x1455771D), (0x81A01B80), (0x12C81090), (0x8162AA0A),
(0x1139F0A7), (0x812A1A36), (0x0FAB26B9), (0x80F66E30), (0x0E1BC326), (0x80C7A813), (0x0C8BD356), (0x809DC971), (0x0AFB67B2), (0x8078D407), (0x096A90AB), (0x8058C955), (0x07D95BB6), (0x803DAA6D), (0x0647D949), (0x80277871), (0x04B618DF), (0x8016343D),
(0x03242AF6), (0x8009DE81), (0x01921D0C), (0x800277A7), (0xFFFFFFA3), (0x80000002), (0xFE6DE338), (0x800277A6), (0xFCDBD54E), (0x8009DE7F), (0xFB49E665), (0x80163444), (0xF9B826FB), (0x8027786E), (0xF826A48E), (0x803DAA69), (0xF6956F99), (0x8058C950),
(0xF5049793), (0x8078D418), (0xF3742CEE), (0x809DC96B), (0xF1E43D1E), (0x80C7A80C), (0xF054D88D), (0x80F66E47), (0xEEC60F9D), (0x812A1A2D), (0xED37EFB3), (0x8162AA00), (0xEBAA8927), (0x81A01B75), (0xEA1DEB4A), (0x81E26C2C), (0xE8922662), (0x82299967),
(0xE70747BB), (0x8275A0C3), (0xE57D5F89), (0x82C67F27), (0xE3F47DF5), (0x831C313B), (0xE26CB031), (0x8376B41E), (0xE0E60653), (0x83D60420), (0xDF608F69), (0x843A1D92), (0xDDDC5B6F), (0x84A2FC55), (0xDC59776E), (0x85109CE4), (0xDAD7F348), (0x8582FAC2),
(0xD957DECD), (0x85FA113A), (0xD7D946E3), (0x8675DC4D), (0xD65C3B40), (0x86F656E9), (0xD4E0CB84), (0x877B7BC6), (0xD367046F), (0x8805466A), (0xD1EEF581), (0x8893B132), (0xD078AD3C), (0x8926B6A0), (0xCF043AF8), (0x89BE50A8), (0xCD91AB39), (0x8A5A7A32),
(0xCC210D35), (0x8AFB2CDB), (0xCAB2700B), (0x8BA06204), (0xC945E00A), (0x8C4A1423), (0xC7DB6C2A), (0x8CF83C44), (0xC6732265), (0x8DAAD3B2), (0xC50D109F), (0x8E61D388), (0xC3A94669), (0x8F1D33C8), (0xC247CDF0), (0x8FDCEF16), (0xC0E8B69C), (0x90A0FD21),
(0xBF8C0DF6), (0x9169565A), (0xBE31E16E), (0x9235F309), (0xBCDA3E5E), (0x9306CB49), (0xBB853205), (0x93DBD70E), (0xBA32CB35), (0x94B50D09), (0xB8E3139E), (0x95926705), (0xB79619E2), (0x9673DB66), (0xB64BEAD5), (0x9759617B), (0xB5049334), (0x9842F06B),
(0xB3C01F9D), (0x99307F35), (0xB27E9C92), (0x9A2204B0), (0xB140180C), (0x9B177652), (0xB0049B27), (0x9C10CD15), (0xAECC33A5), (0x9D0DFE27), (0xAD96ED91), (0x9E0EFFC3), (0xAC64D4D8), (0x9F13C803), (0xAB35F545), (0xA01C4CD8), (0xAA0A5A88), (0xA128840F),
(0xA8E211A2), (0xA23861F5), (0xA7BD2310), (0xA34BDEC3), (0xA69B9B96), (0xA462EE82), (0xA57D865E), (0xA57D8670), (0xA462EE70), (0xA69B9BA8), (0xA34BDEB2), (0xA7BD2322), (0xA23861E4), (0xA8E211B5), (0xA12883FE), (0xAA0A5A9B), (0xA01C4CC7), (0xAB35F559),
(0x9F13C7F2), (0xAC64D4EB), (0x9E0EFFB3), (0xAD96EDA5), (0x9D0DFE16), (0xAECC33B9), (0x9C10CD05), (0xB0049B3B), (0x9B177642), (0xB1401820), (0x9A2204A1), (0xB27E9CA6), (0x99307F26), (0xB3C01FB2), (0x9842F05C), (0xB5049349), (0x9759616C), (0xB64BEAEA),
(0x9673DB57), (0xB79619F7), (0x959266F7), (0xB8E313B4), (0x94B50E13), (0xBA32C99E), (0x93DBD700), (0xBB85321A), (0x9306CB3C), (0xBCDA3E74), (0x9235F2FC), (0xBE31E184), (0x9169564D), (0xBF8C0E0C), (0x90A0FD14), (0xC0E8B6B2), (0x8FDCEF09), (0xC247CE07),
(0x8F1D34AD), (0xC3A944BC), (0x8E61D37C), (0xC50D10B6), (0x8DAAD3A6), (0xC673227C), (0x8CF83C39), (0xC7DB6C41), (0x8C4A1418), (0xC945E021), (0x8BA061F9), (0xCAB27022), (0x8AFB2C68), (0xCC210E37), (0x8A5A7A8D), (0xCD91AA66), (0x89BE5100), (0xCF043A24),
(0x8926B697), (0xD078AD53), (0x8893B128), (0xD1EEF599), (0x88054661), (0xD3670487), (0x877B7BBD), (0xD4E0CB9C), (0x86F6568E), (0xD65C3C4A), (0x8675DC95), (0xD7D94608), (0x85FA1180), (0xD957DDF1), (0x8582FABB), (0xDAD7F360), (0x85109CDD), (0xDC597787),
(0x84A2FC4F), (0xDDDC5B88), (0x843A1D4B), (0xDF60907A), (0x83D603DC), (0xE0E60764), (0x8376B454), (0xE26CAF51), (0x831C316D), (0xE3F47D14), (0x82C67F21), (0xE57D5FA2), (0x8275A0BE), (0xE70747D4), (0x82299962), (0xE892267C), (0x81E26BFB), (0xEA1DEC5F),
(0x81A01B48), (0xEBAA8A3D), (0x8162AA22), (0xED37EECF), (0x812A1A4C), (0xEEC60EB9), (0x80F66E44), (0xF054D8A6), (0x80C7A809), (0xF1E43D38), (0x809DC968), (0xF3742D08), (0x8078D3FF), (0xF50498AB), (0x8058C93B), (0xF69570B2), (0x803DAA77), (0xF826A3A8),
(0x80277879), (0xF9B82615), (0x80163443), (0xFB49E67F), (0x8009DE7E), (0xFCDBD568), (0x800277A6), (0xFE6DE352), (0x465220BB), (0xC66E1FFC), (0x2C61D356), (0xAFFB176D), (0x88067E23), (0x3796F008), (0xC8D128F3), (0xB661A5FA), (0xF19CE4F0), (0x3F7D0345),
(0xE97E6030), (0x4ABA91CF), (0xDEEDAB0C), (0x35FD1D77), (0xDD636439), (0x2F8B794C), (0x764E1D32), (0x7A8D0B1B), (0x8EACB27F), (0x577AD67B), (0x2B3FE2C5), (0x8ACD024A), (0xEDF5C42C), (0xF1CA951C), (0xB98B6D04), (0x6D12B864), (0x50E74D08), (0x73D1503B),
(0xC7D3F6A9), (0x1F365E97), (0xE1340E59), (0x89D21B9D), (0x9B756733), (0x9024CD50), (0xE2C7FF52), (0xD9F061EF), (0x77151392), (0xA0E4A004), (0x60BE820F), (0xC7603BAF), (0xA62E3DA9), (0xA400E11F), (0x155111D4), (0x9030A374), (0x14185124), (0x8D043A00),
(0x50945258), (0x20A6653E), (0xA19BA8B9), (0x3CFBE692), (0x91471D98), (0x0B9D03FF), (0xA5BA8E29), (0xFFC8EB22), (0x142CB1B0), (0x55317319), (0x33ADC77C), (0xF69BADCE), (0x78423D3C), (0xBE67BBF0), (0x7D88BD15), (0x1E9EF673), (0x512098B3), (0x326A3D3D),
(0x161EF3B6), (0x0E4C4ED5), (0x4E9F9982), (0xD0AB6A36), (0xF17DD377), (0x5E53F31C), (0xBE9112EA), (0x3DE1B0FF), (0xD7F39AE7), (0xA34B9635), (0x49CFEE6C), (0x8A063AC6), (0xBA79BC0D), (0x6A588782), (0x439BE593), (0x0A5F7E8D), (0xD2FC2561), (0x4698AC8F),
(0x247C3BC2), (0x5826523D), (0x4D2BD753), (0x4B8BCC17), (0x9B743503), (0x9F84E434), (0x8B97B41A), (0x3CFCE75F), (0x32D2D487), (0x3E80B4AF), (0x42F8B0EA), (0xEC0C81EE), (0x17279309), (0x681FA0F3), (0x8BAD208E), (0xD11792C6), (0xF1FF07B3), (0x21338050),
(0x8A1A21A4), (0xFF0358D8), (0x416C13C1), (0x46348858), (0x83820519), (0xABC08822), (0x6A1A5200), (0x0C83122A), (0xA67FDF06), (0x0629F970), (0xEFAA2743), (0x0C00AD95), (0xFF09F5CF), (0xC784DB8B), (0xCB00BA7D), (0x6220909F), (0x7149B7DF), (0x5BD3EBEC),
(0x0485EF71), (0x07FE23B1), (0x80737668), (0x42A32645), (0xD6EDBB18), (0x407E72C5), (0x901402E0), (0x0C1B7905), (0x01CE11FC), (0x00C64914), (0x3037978A), (0xFABC9190), (0x43A0AC89), (0xB9998370), (0xD6D69209), (0xF132455E), (0x9DAFF482), (0xA2D635C2),
(0xBD976B5E), (0x79ED93ED), (0x7D42CA22), (0x2E23F9C1), (0xCF0E7521), (0x96D88AC1), (0x420C2845), (0x1E19573D), (0xF8FE9279), (0xC1495100), (0x201249C2), (0x8B183723), (0x450615A5), (0x004C0112), (0x85EB5235), (0x1BD8BD48), (0xA2914F03), (0x51420EAC),
(0xCE788470), (0x0508030B), (0xA4CD2A6C), (0x9626C062), (0xF88D2DE5), (0xA9D6B399), (0xB3C57BFD), (0xCB1E9C4C), (0x004A5094), (0xD0CBC42A), (0x3BE1A694), (0x3C353FA7), (0xA5697C57), (0xFE961665), (0xED6B7C7D), (0x433DCF8D), (0xDA7D6C24), (0x407112C1),
(0x4B9D285D), (0x6C9A2F57), (0x1BF9FAEF), (0x4EE3E2E9), (0xE5AE7E92), (0x60A11D3F), (0x4B441B63), (0xC8419BB1), (0x2C4F6DDD), (0xCEC3CF6D), (0x325A65CA), (0x818FC6A4), (0xC55E5E5B), (0x277445A3), (0x6B3289F7), (0x09147D55), (0x4F378D53), (0x588B0DC8),
(0xF70BA347), (0xCBC43FAE), (0xD98E6639), (0x07AF9ED0), (0xE2E6305A), (0x5E003571), (0x18523E7A), (0xF70D1E27), (0xD7288950), (0x12C11132), (0xFF1C9D17), (0x318050F1), (0x1A219821), (0xBF21998A), (0x6C13C1FF), (0x8C605861), (0x0A183954), (0x7BE4C1B6),
(0xA2CBC410), (0x7CC00A55), (0x5E58A973), (0x1122EF86), (0x80568E39), (0x2310C300), (0x0E152D0E), (0x9E017C1C), (0x851DFC26), (0xD1B1E662), (0x81BA10B0), (0x96C2E977), (0x8688E419), (0x97C94989), (0x250D535E), (0x35FDE732), (0x147AD183), (0xCF9D75C0),
(0x6B2C183C), (0x1D326489), (0xC0807D98), (0xC081622E), (0x15C87B87), (0x7404598C), (0xFC3C7FA6), (0x28BA8CF5), (0xE311EA9B), (0xEABFD4A5), (0x8768834A), (0x0F7D7440), (0x40593152), (0xCF1E8BF5), (0xE32017B6), (0x39E66070), (0x560ECE9D), (0xF4132C77),
(0x722945C1), (0x96ECB5D5), (0x0C100C06), (0x30E96C02), (0xE0B0159B), (0x163A40CB), (0x1B28B750), (0x11420E11), (0x34DC5981), (0xD2769C3C), (0x569402D0), (0xE4F157C0), (0x120DF24C), (0x40D86153), (0x1942CD11), (0x22484C3B), (0x8B299144), (0x58F1AAF8),
(0xB2CD4777), (0x4A53AB02), (0x55A5DAD4), (0xC8D58D79), (0x6CAEC011), (0x1C955C84), (0xDE08A6C6), (0xD402AF7D), (0x695E6FA4), (0x4DA863B9), (0x5118070C), (0x5A97EF1C), (0x8880B388), (0x4D596B2D), (0x37D74DCF), (0x58138B38), (0x5B7BCD4E), (0xAE6C4422),
(0x4E5D1AF1), (0xDE7396A5), (0xB442F217), (0x0DF7A006), (0x96EEF8A2), (0xE2847B26), (0x68B9F67B), (0x8FE1FA65), (0xE0A87E56), (0x1BE8FC4C), (0xBBD65F9D), (0xE6F02C9D), (0x867C6EEC), (0x43FAB725), (0xA4144419), (0x69BB6AAF), (0xCE27B744), (0x022241A9),
(0xC05DF14E), (0x8050F1FF), (0x218C2131), (0xBE25941A), (0x9041CD06), (0x1EE8A9C0), (0xD530FEDD), (0x7495C808), (0x050918A6), (0x3CE732E9), (0xF87262FC), (0x01B812F1), (0x1FBA4842), (0x3E86C7F2), (0x270DB358), (0xF9D22834), (0x74D4F469), (0x9A6E87D2),
(0x8CBB0A50), (0xC0784B92), (0x278DA581), (0x9221A7D5), (0xDE801B54), (0x4B531FDA), (0xC8B91F91), (0x7A90DA40), (0x2967A429), (0x50CC1D8D), (0x9AE9110E), (0x50F250EE), (0xD695B072), (0xFC7A5718), (0x1F1A26E6), (0x7D5ADD4D), (0xC8978597), (0xEB326DC3),
(0x384E9824), (0xC3B9D78C), (0xD8D2E214), (0xEFFBB048), (0x00AB7714), (0x9D89CA13), (0xF70688D0), (0xADDA3A37), (0x5FBE5943), (0x4ED9D613), (0x8A9DCC93), (0x19814236), (0x09A68BCC), (0xCEAC33AC), (0x0BA70BB3), (0x326272E1), (0x2A8C6D20), (0x23083D0B),
(0xFA809C02), (0x5825C6C2), (0x061B4695), (0x8DB97748), (0x5071E584), (0x08760959), (0x44EA8D04), (0x5CE6E2AA), (0x009AD176), (0xAEC64F19), (0xCADDA632), (0x8D795445), (0x16E3EA24), (0xF12648CA), (0xD5CC350C), (0x615EBAD7), (0x4E769FB6), (0x6C5EB380),
(0x5BB46425), (0xEAB6C77C), (0x10945408), (0xC1291B86), (0x913B71A5), (0x003BA329), (0x91812854), (0xE200CB12), (0x7A4696A8), (0x76E55D25), (0xD6D3C577), (0x8B1B79B1), (0x296B04CD), (0xA024CC35), (0x3341A477), (0x8EC12358), (0xF5982B2F), (0x36F120C9),
(0x79DBE799), (0xED74F092), (0xA1031AE3), (0xB2D98C6B), (0x358CAB73), (0x41153C19), (0x474D4548), (0xA7E834A9), (0x999738AE), (0x50F1FFE8), (0x88612F80), (0xFD931A21), (0x23839ABD), (0xBE604701), (0x812274B9), (0x4CA9EA32), (0xE5F2D207), (0xC1CF4653),
(0x43561B23), (0x53B2E277), (0x4F2B00EC), (0xF2B3BA80), (0xFC708CC5), (0xE7F63F00), (0x26120342), (0x1A49C7A8), (0xA9350A7C), (0xE530A06C), (0x5CB9E550), (0xB1672913), (0x65FA76F9), (0xC9F92DCA), (0x7D4E9195), (0x4DBB8265), (0x2036E26C), (0xEDC026E6),
(0xB9A617E0), (0x6469F0EE), (0xE9DAABB2), (0x26063210), (0xAC545DF4), (0x134E035A), (0x216EDE10), (0xEC81158D), (0x122A2CAE), (0xA263D0E1), (0x6CA1FB28), (0xBE287DF0), (0x548104E8), (0x35987D93), (0xBC3E549C), (0x3ACEFD64), (0x3348CD92), (0x914472FD),
(0x80F28098), (0xDF3FABCD), (0x24D840AC), (0xBA95321C), (0xDC211D20), (0x15041942), (0x61858ABD), (0x546084A1), (0xCEDC07F4), (0xEAB2DE00), (0x045BC1E8), (0x82B2AE86), (0xE4BF2823), (0x9A01B47A), (0x1769438E), (0x015C7986), (0x5FDFF97D), (0x5F28D62C),
(0x83C84C8C), (0xDBAC601A), (0x1AE2844F), (0x79F9DC1C), (0x01C3AB5C), (0xC971D839), (0x44DEA398), (0xC0A64EA1), (0x387E24A5), (0x0600D59F), (0x5169B371), (0xEC782B6B), (0xF7FF769D), (0x317B0AE8), (0xE33846C7), (0x2FE05849), (0x03273853), (0x2ED27A88),
(0x24DF4343), (0xFB03C70F), (0x82783231), (0xD31C0D29), (0xADC5BD8B), (0x7D024F1B), (0x91C18D19), (0xA8EE58E2), (0x4C23A59F), (0xC61DCA9F), (0x03BC91EC), (0x51986147), (0x05B8BE24), (0x872AC918), (0x8050F1FF), (0x2184812F), (0xB20D941A), (0x9141CB0C),
(0xF600A340), (0x80E82AC2), (0x66028631), (0x120FFA45), (0x889F24CB), (0x81F0BB27), (0x3100D51A), (0x2309A0B3), (0xAE004718), (0xAA584A8D), (0x9432DD2E), (0xDD0E93FA), (0xC12993A6), (0x6E28035D), (0x9BF7772B), (0x1D608C32)
};


static const INT __twiddles_mips_fft32_1024[] =
{
(0x7FFFFFFF), (0x00000000), (0x7FFF6215), (0xFF36F078), (0x7FFD8859), (0xFE6DE2E0), (0x7FFA72D0), (0xFDA4D929), (0x7FF62181), (0xFCDBD540), (0x7FF09476), (0xFC12D91B), (0x7FE9CBBE), (0xFB49E6A3), (0x7FE1C76A), (0xFA80FFCE), (0x7FD8878C), (0xF9B82681),
(0x7FCE0C3D), (0xF8EF5CBC), (0x7FC25595), (0xF826A464), (0x7FB563B2), (0xF75DFF6B), (0x7FA736B2), (0xF6956FB7), (0x7F97CEBB), (0xF5CCF73E), (0x7F872BF2), (0xF5049800), (0x7F754E7E), (0xF43C53CB), (0x7F62368D), (0xF3742C9D), (0x7F4DE450), (0xF2AC2473),
(0x7F3857F4), (0xF1E43D1C), (0x7F2191B2), (0xF11C7895), (0x7F0991C3), (0xF054D8DA), (0x7EF05860), (0xEF8D5FC8), (0x7ED5E5C6), (0xEEC60F3C), (0x7EBA3A38), (0xEDFEE930), (0x7E9D55FB), (0xED37EF91), (0x7E7F3954), (0xEC71244A), (0x7E5FE490), (0xEBAA8944),
(0x7E3F5800), (0xEAE4208A), (0x7E1D93EA), (0xEA1DEBC6), (0x7DFA98A7), (0xE957ED00), (0x7DD6668D), (0xE8922621), (0x7DB0FDF5), (0xE7CC9912), (0x7D8A5F3C), (0xE70747B9), (0x7D628AC7), (0xE642341C), (0x7D3980ED), (0xE57D5FE4), (0x7D0F4217), (0xE4B8CD16),
(0x7CE3CEB0), (0xE3F47D95), (0x7CB72721), (0xE3307347), (0x7C894BDA), (0xE26CB010), (0x7C5A3D52), (0xE1A935F1), (0x7C29FBEF), (0xE0E6068F), (0x7BF88830), (0xE02323EA), (0x7BC5E296), (0xDF609002), (0x7B920B86), (0xDE9E4C5B), (0x7B5D03A1), (0xDDDC5B4F),
(0x7B26CB49), (0xDD1ABE41), (0x7AEF6325), (0xDC59778B), (0x7AB6CB9A), (0xDB98888E), (0x7A7D0559), (0xDAD7F3A2), (0x7A4210DE), (0xDA17BA63), (0x7A05EEA8), (0xD957DE6F), (0x79C89F71), (0xD898621A), (0x798A23A8), (0xD7D946C3), (0x794A7C11), (0xD71A8EBA),
(0x7909A935), (0xD65C3B98), (0x78C7AB9E), (0xD59E4EF9), (0x78848419), (0xD4E0CB28), (0x78403321), (0xD423B181), (0x77FAB98A), (0xD367044F), (0x77B417D4), (0xD2AAC4EB), (0x776C4ED9), (0xD1EEF59D), (0x77235F35), (0xD13397FA), (0x76D94983), (0xD078AD93),
(0x768E0EA9), (0xCFBE38AD), (0x7641AF32), (0xCF043A9E), (0x75F42C0B), (0xCE4AB5A6), (0x75A585D9), (0xCD91AB55), (0x7555BD47), (0xCCD91D37), (0x7504D34C), (0xCC210D8B), (0x74B2C87B), (0xCB697DA0), (0x745F9DD3), (0xCAB26FB2), (0x740B53ED), (0xC9FBE50E),
(0x73B5EBCF), (0xC945DFEB), (0x735F662F), (0xC89061D1), (0x7307C3C9), (0xC7DB6C45), (0x72AF05AB), (0xC727017A), (0x72552C79), (0xC67322B9), (0x71FA3948), (0xC5BFD232), (0x719E2CDE), (0xC50D1164), (0x71410800), (0xC45AE1D1), (0x70E2CBCD), (0xC3A945A1),
(0x708378F4), (0xC2F83E1B), (0x7023109C), (0xC247CD62), (0x6FC19376), (0xC197F4BB), (0x6F5F02CF), (0xC0E8B67E), (0x6EFB5F1D), (0xC03A137E), (0x6E96A995), (0xBF8C0DD8), (0x6E30E32F), (0xBEDEA73A), (0x6DCA0D27), (0xBE31E1BF), (0x6D6227FA), (0xBD85BE33),
(0x6CF934E8), (0xBCDA3EAE), (0x6C8F3538), (0xBC2F6544), (0x6C242969), (0xBB8532C0), (0x6BB812C5), (0xBADBA934), (0x6B4AF258), (0xBA32CA42), (0x6ADCC976), (0xB98A97F6), (0x6A6D98A1), (0xB8E31318), (0x69FD6132), (0xB83C3DB1), (0x698C2487), (0xB79619C6),
(0x6919E326), (0xB6F0A81D), (0x68A69E72), (0xB64BEAB9), (0x68325786), (0xB5A7E331), (0x67BD0FCC), (0xB5049380), (0x6746C7D1), (0xB461FC6A), (0x66CF8103), (0xB3C01FE9), (0x66573CD5), (0xB31EFFF0), (0x65DDFBD6), (0xB27E9D43), (0x6563BF7E), (0xB1DEF9D2),
(0x64E8894A), (0xB140178C), (0x646C59CC), (0xB0A1F730), (0x63EF3286), (0xB0049AA9), (0x637114AB), (0xAF68037B), (0x62F201C4), (0xAECC338B), (0x6271FA69), (0xAE312B94), (0x61F10027), (0xAD96ED77), (0x616F148E), (0xACFD7B13), (0x60EC383A), (0xAC64D51F),
(0x60686CC0), (0xABCCFD75), (0x5FE3B366), (0xAB35F58C), (0x5F5E0DC8), (0xAA9FBF38), (0x5ED77C86), (0xAA0A5B2C), (0x5E500140), (0xA975CB39), (0x5DC79D9C), (0xA8E2112C), (0x5D3E523E), (0xA84F2DB4), (0x5CB420CD), (0xA7BD229A), (0x5C290AA0), (0xA72BF148),
(0x5B9D1166), (0xA69B9B7D), (0x5B1035C7), (0xA60C21E8), (0x5A827978), (0xA57D8646), (0x59F3DE30), (0xA4EFCA51), (0x5964649B), (0xA462EEB2), (0x58D40E75), (0xA3D6F51F), (0x5842DD7E), (0xA34BDF4B), (0x57B0D265), (0xA2C1ADDA), (0x571DEEED), (0xA238627B),
(0x568A3482), (0xA1AFFE81), (0x55F5A4ED), (0xA1288391), (0x556040E2), (0xA0A1F24F), (0x54CA0A2E), (0xA01C4C5C), (0x543302A5), (0x9F979356), (0x539B2AFB), (0x9F13C7DC), (0x53028507), (0x9E90EB88), (0x52691241), (0x9E0EFF9D), (0x51CED486), (0x9D8E05AD),
(0x5133CC8F), (0x9D0DFE52), (0x5097FC3C), (0x9C8EEB1A), (0x4FFB6572), (0x9C10CD90), (0x4F5E08EB), (0x9B93A649), (0x4EBFE88F), (0x9B1776CB), (0x4E2105E4), (0x9A9C4048), (0x4D8162D8), (0x9A22043F), (0x4CE1002B), (0x99A8C340), (0x4C3FDFCC), (0x99307EC5),
(0x4B9E03B1), (0x98B93843), (0x4AFB6C9B), (0x9842F048), (0x4A581C83), (0x97CDA844), (0x49B41562), (0x975961A2), (0x490F57FF), (0x96E61CED), (0x4869E657), (0x9673DB8C), (0x47C3C202), (0x96029E99), (0x471CED05), (0x95926772), (0x46756827), (0x9523369D),
(0x45CD356F), (0x94B50D74), (0x452456E9), (0x9447ED4D), (0x447ACD5D), (0x93DBD6AA), (0x43D09AD9), (0x9370CADA), (0x4325C102), (0x9306CAE7), (0x427A417D), (0x929DD7D5), (0x41CE1ECD), (0x9235F32C), (0x412158E3), (0x91CF1CE3), (0x4073F245), (0x9169567D),
(0x3FC5ECA0), (0x9104A0F4), (0x3F17499F), (0x90A0FD42), (0x3E680AF3), (0x903E6C5D), (0x3DB8324C), (0x8FDCEF37), (0x3D07C23C), (0x8F7C873A), (0x3C56BAB5), (0x8F1D3461), (0x3BA51E4D), (0x8EBEF810), (0x3AF2EEBA), (0x8E61D331), (0x3A402DB4), (0x8E05C6AA),
(0x398CDCF3), (0x8DAAD35D), (0x38D8FE32), (0x8D50FA2B), (0x38249413), (0x8CF83C62), (0x376F9E88), (0x8CA099FB), (0x36BA2034), (0x8C4A1440), (0x36041AD7), (0x8BF4AC06), (0x354D9033), (0x8BA06221), (0x3496820A), (0x8B4D375F), (0x33DEF21F), (0x8AFB2C8E),
(0x3326E323), (0x8AAA42E0), (0x326E5505), (0x8A5A7A4D), (0x31B54A79), (0x8A0BD403), (0x30FBC547), (0x89BE50C2), (0x3041C737), (0x8971F14B), (0x2F875216), (0x8926B65A), (0x2ECC67AF), (0x88DCA0A9), (0x2E110ABF), (0x8893B14A), (0x2D553B35), (0x884BE838),
(0x2C98FBD1), (0x88054682), (0x2BDC4E63), (0x87BFCCD5), (0x2B1F34BC), (0x877B7BDD), (0x2A61B0AF), (0x87385443), (0x29A3C40F), (0x86F656AC), (0x28E571A3), (0x86B5840E), (0x2826B95E), (0x8675DC62), (0x27679E06), (0x8637609A), (0x26A82174), (0x85FA114F),
(0x25E84581), (0x85BDEF19), (0x25280C05), (0x8582FA8C), (0x246777D0), (0x85493482), (0x23A688D3), (0x85109CF7), (0x22E541E0), (0x84D934C0), (0x2223A4D2), (0x84A2FC68), (0x2161B389), (0x846DF472), (0x209F6FE1), (0x843A1D62), (0x1FDCDBBB), (0x840777B9),
(0x1F19F9F0), (0x83D60431), (0x1E56CA6E), (0x83A5C2C5), (0x1D935011), (0x8376B42E), (0x1CCF8CBB), (0x8348D8DF), (0x1C0B824E), (0x831C314A), (0x1B4732AE), (0x82F0BDDB), (0x1A829FC0), (0x82C67F00), (0x19BDCC63), (0x829D7553), (0x18F8B888), (0x8275A0D1),
(0x18336710), (0x824F0211), (0x176DD9E1), (0x82299974), (0x16A812E3), (0x82056754), (0x15E213FD), (0x81E26C0B), (0x151BDF19), (0x81C0A7F1), (0x1455771D), (0x81A01B80), (0x138EDBF8), (0x8180C6B6), (0x12C81090), (0x8162AA0A), (0x120116D2), (0x8145C5C8),
(0x1139F0A7), (0x812A1A36), (0x10729FFB), (0x810FA798), (0x0FAB26B9), (0x80F66E30), (0x0EE387CD), (0x80DE6E5A), (0x0E1BC326), (0x80C7A813), (0x0D53DBAF), (0x80B21BB4), (0x0C8BD356), (0x809DC971), (0x0BC3AC07), (0x808AB17E), (0x0AFB67B2), (0x8078D407),
(0x0A330844), (0x8068313B), (0x096A90AB), (0x8058C955), (0x08A200D7), (0x804A9C53), (0x07D95BB6), (0x803DAA6D), (0x0710A337), (0x8031F3C3), (0x0647D949), (0x80277871), (0x057EFFDC), (0x801E3892), (0x04B618DF), (0x8016343D), (0x03ED2743), (0x800F6B8D),
(0x03242AF6), (0x8009DE81), (0x025B26E9), (0x80058D31), (0x01921D0C), (0x800277A7), (0x00C90F4F), (0x80009DEB), (0xFFFFFFA3), (0x80000002), (0xFF36F0F5), (0x80009DEB), (0xFE6DE338), (0x800277A6), (0xFDA4D95B), (0x80058D2F), (0xFCDBD54E), (0x8009DE7F),
(0xFC12D902), (0x800F6B8A), (0xFB49E665), (0x80163444), (0xFA80FF69), (0x801E389A), (0xF9B826FB), (0x8027786E), (0xF8EF5D0E), (0x8031F3BF), (0xF826A48E), (0x803DAA69), (0xF75DFF6D), (0x804A9C4E), (0xF6956F99), (0x8058C950), (0xF5CCF701), (0x8068314A),
(0xF5049793), (0x8078D418), (0xF43C543D), (0x808AB177), (0xF3742CEE), (0x809DC96B), (0xF2AC2495), (0x80B21BAD), (0xF1E43D1E), (0x80C7A80C), (0xF11C7877), (0x80DE6E52), (0xF054D88D), (0x80F66E47), (0xEF8D5F4B), (0x810FA7B0), (0xEEC60F9D), (0x812A1A2D),
(0xEDFEE972), (0x8145C5BE), (0xED37EFB3), (0x8162AA00), (0xEC71244C), (0x8180C6AB), (0xEBAA8927), (0x81A01B75), (0xEAE4202D), (0x81C0A810), (0xEA1DEB4A), (0x81E26C2C), (0xE957ED61), (0x82056748), (0xE8922662), (0x82299967), (0xE7CC9933), (0x824F0204),
(0xE70747BB), (0x8275A0C3), (0xE64233E0), (0x829D7545), (0xE57D5F89), (0x82C67F27), (0xE4B8CC9B), (0x82F0BE03), (0xE3F47DF5), (0x831C313B), (0xE3307388), (0x8348D8D0), (0xE26CB031), (0x8376B41E), (0xE1A935D4), (0x83A5C2B5), (0xE0E60653), (0x83D60420),
(0xE023238F), (0x840777E8), (0xDF608F69), (0x843A1D92), (0xDE9E4CB9), (0x846DF460), (0xDDDC5B6F), (0x84A2FC55), (0xDD1ABE62), (0x84D934AE), (0xDC59776E), (0x85109CE4), (0xDB988872), (0x8549346E), (0xDAD7F348), (0x8582FAC2), (0xDA17BAC1), (0x85BDEF05),
(0xD957DECD), (0x85FA113A), (0xD898623B), (0x86376085), (0xD7D946E3), (0x8675DC4D), (0xD71A8E9D), (0x86B583F8), (0xD65C3B40), (0x86F656E9), (0xD59E4EA0), (0x87385481), (0xD4E0CB84), (0x877B7BC6), (0xD423B1DD), (0x87BFCCBD), (0xD367046F), (0x8805466A),
(0xD2AAC50A), (0x884BE820), (0xD1EEF581), (0x8893B132), (0xD13397A2), (0x88DCA0EE), (0xD078AD3C), (0x8926B6A0), (0xCFBE3908), (0x8971F132), (0xCF043AF8), (0x89BE50A8), (0xCE4AB5C6), (0x8A0BD3E8), (0xCD91AB39), (0x8A5A7A32), (0xCCD91D1C), (0x8AAA42C5),
(0xCC210D35), (0x8AFB2CDB), (0xCB697D4B), (0x8B4D37AC), (0xCAB2700B), (0x8BA06204), (0xC9FBE567), (0x8BF4ABE9), (0xC945E00A), (0x8C4A1423), (0xC89061B6), (0x8CA099DE), (0xC7DB6C2A), (0x8CF83C44), (0xC7270126), (0x8D50FA7F), (0xC6732265), (0x8DAAD3B2),
(0xC5BFD1A5), (0x8E05C6FF), (0xC50D109F), (0x8E61D388), (0xC45AE10D), (0x8EBEF868), (0xC3A94669), (0x8F1D33C8), (0xC2F83EE1), (0x8F7C86A0), (0xC247CDF0), (0x8FDCEF16), (0xC197F548), (0x903E6C3B), (0xC0E8B69C), (0x90A0FD21), (0xC03A139B), (0x9104A0D2),
(0xBF8C0DF6), (0x9169565A), (0xBEDEA758), (0x91CF1CC0), (0xBE31E16E), (0x9235F309), (0xBD85BDE2), (0x929DD837), (0xBCDA3E5E), (0x9306CB49), (0xBC2F6487), (0x9370CB3D), (0xBB853205), (0x93DBD70E), (0xBADBA879), (0x9447EDB3), (0xBA32CB35), (0x94B50D09),
(0xB98A987D), (0x95233631), (0xB8E3139E), (0x95926705), (0xB83C3E37), (0x96029E73), (0xB79619E2), (0x9673DB66), (0xB6F0A839), (0x96E61CC6), (0xB64BEAD5), (0x9759617B), (0xB5A7E34D), (0x97CDA867), (0xB5049334), (0x9842F06B), (0xB461FC1F), (0x98B93866),
(0xB3C01F9D), (0x99307F35), (0xB31EFF3F), (0x99A8C3B0), (0xB27E9C92), (0x9A2204B0), (0xB1DEF922), (0x9A9C4109), (0xB140180C), (0x9B177652), (0xB0A1F7AF), (0x9B93A5CF), (0xB0049B27), (0x9C10CD15), (0xAF6803F9), (0x9C8EEAEF), (0xAECC33A5), (0x9D0DFE27),
(0xAE312BAE), (0x9D8E0581), (0xAD96ED91), (0x9E0EFFC3), (0xACFD7ACC), (0x9E90EBAF), (0xAC64D4D8), (0x9F13C803), (0xABCCFD2E), (0x9F97937D), (0xAB35F545), (0xA01C4CD8), (0xAA9FBE92), (0xA0A1F2CC), (0xAA0A5A88), (0xA128840F), (0xA975CC0F), (0xA1AFFDFC),
(0xA8E211A2), (0xA23861F5), (0xA84F2E2A), (0xA2C1AD53), (0xA7BD2310), (0xA34BDEC3), (0xA72BF1BC), (0xA3D6F4F0), (0xA69B9B96), (0xA462EE82), (0xA60C2200), (0xA4EFCA21), (0xA57D865E), (0xA57D8670), (0xA4EFCA0F), (0xA60C2213), (0xA462EE70), (0xA69B9BA8),
(0xA3D6F4DE), (0xA72BF1CF), (0xA34BDEB2), (0xA7BD2322), (0xA2C1AD42), (0xA84F2E3D), (0xA23861E4), (0xA8E211B5), (0xA1AFFF45), (0xA975CAA9), (0xA12883FE), (0xAA0A5A9B), (0xA0A1F2BB), (0xAA9FBEA5), (0xA01C4CC7), (0xAB35F559), (0x9F97936C), (0xABCCFD41),
(0x9F13C7F2), (0xAC64D4EB), (0x9E90EB9E), (0xACFD7ADF), (0x9E0EFFB3), (0xAD96EDA5), (0x9D8E0571), (0xAE312BC2), (0x9D0DFE16), (0xAECC33B9), (0x9C8EEADF), (0xAF68040D), (0x9C10CD05), (0xB0049B3B), (0x9B93A5BF), (0xB0A1F7C3), (0x9B177642), (0xB1401820),
(0x9A9C40F9), (0xB1DEF936), (0x9A2204A1), (0xB27E9CA6), (0x99A8C3A1), (0xB31EFF54), (0x99307F26), (0xB3C01FB2), (0x98B93857), (0xB461FC33), (0x9842F05C), (0xB5049349), (0x97CDA858), (0xB5A7E362), (0x9759616C), (0xB64BEAEA), (0x96E61CB8), (0xB6F0A84E),
(0x9673DB57), (0xB79619F7), (0x96029E64), (0xB83C3E4C), (0x959266F7), (0xB8E313B4), (0x95233623), (0xB98A9893), (0x94B50E13), (0xBA32C99E), (0x9447EDA5), (0xBADBA88F), (0x93DBD700), (0xBB85321A), (0x9370CB30), (0xBC2F649D), (0x9306CB3C), (0xBCDA3E74),
(0x929DD829), (0xBD85BDF8), (0x9235F2FC), (0xBE31E184), (0x91CF1CB3), (0xBEDEA76E), (0x9169564D), (0xBF8C0E0C), (0x9104A0C5), (0xC03A13B2), (0x90A0FD14), (0xC0E8B6B2), (0x903E6C2F), (0xC197F55F), (0x8FDCEF09), (0xC247CE07), (0x8F7C8694), (0xC2F83EF8),
(0x8F1D34AD), (0xC3A944BC), (0x8EBEF85C), (0xC45AE123), (0x8E61D37C), (0xC50D10B6), (0x8E05C6F4), (0xC5BFD1BC), (0x8DAAD3A6), (0xC673227C), (0x8D50FA73), (0xC727013D), (0x8CF83C39), (0xC7DB6C41), (0x8CA099D3), (0xC89061CD), (0x8C4A1418), (0xC945E021),
(0x8BF4ABDF), (0xC9FBE57E), (0x8BA061F9), (0xCAB27022), (0x8B4D3738), (0xCB697E4C), (0x8AFB2C68), (0xCC210E37), (0x8AAA4254), (0xCCD91E1E), (0x8A5A7A8D), (0xCD91AA66), (0x8A0BD442), (0xCE4AB4F1), (0x89BE5100), (0xCF043A24), (0x8971F188), (0xCFBE3833),
(0x8926B697), (0xD078AD53), (0x88DCA0E4), (0xD13397BA), (0x8893B128), (0xD1EEF599), (0x884BE817), (0xD2AAC522), (0x88054661), (0xD3670487), (0x87BFCCB5), (0xD423B1F5), (0x877B7BBD), (0xD4E0CB9C), (0x87385424), (0xD59E4FAA), (0x86F6568E), (0xD65C3C4A),
(0x86B5839E), (0xD71A8FA8), (0x8675DC95), (0xD7D94608), (0x863760CC), (0xD8986160), (0x85FA1180), (0xD957DDF1), (0x85BDEF49), (0xDA17B9E5), (0x8582FABB), (0xDAD7F360), (0x85493467), (0xDB98888A), (0x85109CDD), (0xDC597787), (0x84D934A7), (0xDD1ABE7A),
(0x84A2FC4F), (0xDDDC5B88), (0x846DF45A), (0xDE9E4CD2), (0x843A1D4B), (0xDF60907A), (0x840777A2), (0xE02324A0), (0x83D603DC), (0xE0E60764), (0x83A5C2EC), (0xE1A934F4), (0x8376B454), (0xE26CAF51), (0x8348D904), (0xE33072A7), (0x831C316D), (0xE3F47D14),
(0x82F0BDFE), (0xE4B8CCB4), (0x82C67F21), (0xE57D5FA2), (0x829D7540), (0xE64233F9), (0x8275A0BE), (0xE70747D4), (0x824F0200), (0xE7CC994C), (0x82299962), (0xE892267C), (0x82056743), (0xE957ED7A), (0x81E26BFB), (0xEA1DEC5F), (0x81C0A7E1), (0xEAE42143),
(0x81A01B48), (0xEBAA8A3D), (0x8180C6CF), (0xEC712368), (0x8162AA22), (0xED37EECF), (0x8145C5DF), (0xEDFEE88E), (0x812A1A4C), (0xEEC60EB9), (0x810FA7AD), (0xEF8D5F65), (0x80F66E44), (0xF054D8A6), (0x80DE6E4F), (0xF11C7891), (0x80C7A809), (0xF1E43D38),
(0x80B21BAA), (0xF2AC24AF), (0x809DC968), (0xF3742D08), (0x808AB175), (0xF43C5456), (0x8078D3FF), (0xF50498AB), (0x80683134), (0xF5CCF819), (0x8058C93B), (0xF69570B2), (0x804A9C5E), (0xF75DFE87), (0x803DAA77), (0xF826A3A8), (0x8031F3CB), (0xF8EF5C28),
(0x80277879), (0xF9B82615), (0x801E3899), (0xFA80FF82), (0x80163443), (0xFB49E67F), (0x800F6B8A), (0xFC12D91B), (0x8009DE7E), (0xFCDBD568), (0x80058D2F), (0xFDA4D975), (0x800277A6), (0xFE6DE352), (0x80009DEA), (0xFF36F10F), (0x656DBCC8), (0x7A9108CA),
(0xBA3670F0), (0x699195A2), (0x172D2FD0), (0xBE386D51), (0xC4AEA51C), (0x2AA77D7D), (0x8B51D3B4), (0xCF490FD9), (0x4A7BEBCA), (0x91072E04), (0xA175872D), (0x54464A9E), (0x67A6CD02), (0x9E8204A8), (0x10C14BF6), (0x75ACDA16), (0xF78D7B3E), (0x6AB3CF69),
(0xA4D8C756), (0xCB8FCD24), (0x75CC5713), (0xC13E5AE3), (0xA8C3176D), (0x5EE6D416), (0x80C5DA0E), (0xD84A007C), (0x719E73C0), (0x765014B0), (0x0A391BC8), (0x60A70223), (0x54CDA46B), (0x4606D455), (0xA606FB2A), (0xE4CE1557), (0x73240820), (0x8037A8B4),
(0xA1D88032), (0x75A3F1C7), (0x32BB0370), (0x5F850242), (0x85EC8F5B), (0x8AA2B33C), (0x91B370C2), (0x938E9DDB), (0xE6B6983E), (0x49602AA9), (0x6C2D9965), (0x5B16AE72), (0x69579B6B), (0xCD3AB415), (0x5B7CF03C), (0xD7B27637), (0x2D69B79A), (0x3237B468),
(0x5F94D650), (0xAB37106D), (0x0B831457), (0xAFD80334), (0xB5A0B27B), (0x16B763E9), (0x3B454756), (0x3167CD98), (0x707239AF), (0x0A4F132A), (0x2782C8CA), (0x1AC1DD4E), (0x121CADFE), (0x9A911D90), (0x0157ADD6), (0x1559BB95), (0x2854C84A), (0xD97E3685),
(0x76308AA0), (0x8050F1FF), (0x2180612F), (0xFFFB8A1A), (0x0BC1FFFF), (0x1032846C), (0x002A30C2), (0x654C58F2), (0xA58ABACC), (0xC3E9831C), (0x27531B8C), (0xADBB8698), (0x06801C12), (0xAFAEEC8E), (0xC3269291), (0x5C2E01A0), (0x4A61087C), (0x60F21E7B),
(0x6B92C670), (0xC9872364), (0x48464C97), (0x342E6499), (0xA6849A48), (0xF034002D), (0x442B4488), (0x160B0442), (0xED52CF50), (0x4D436C05), (0x6841C602), (0xA14F8A82), (0x9AC20DDE), (0x5B9251EE), (0x8ED998D5), (0x92B6EA22), (0x5B136C4B), (0x97C0A45B),
(0x459C94A4), (0xCD9583C0), (0x6C589DF8), (0x3C9A17CD), (0x906601BF), (0x84D612E1), (0x67176F5F), (0xE47D5A44), (0x37386D5E), (0x92A4D61B), (0x28C513FB), (0x6E91599A), (0x653DA16D), (0xA19C39C0), (0x427690AD), (0x7005461C), (0xAB6C1EE4), (0x80BACC2D),
(0x780A18C5), (0x822DC774), (0xB6043036), (0x00789C18), (0x106C9507), (0x4103A864), (0xC511585B), (0xF3E9B7F8), (0x24D79C4D), (0x31B20EB5), (0xB1C0483E), (0xE25C56B9), (0x96991096), (0x7CD4121F), (0x8A3B83F4), (0x704271C2), (0xB5C26ACC), (0x2B131AF0),
(0x549CB16C), (0x74649E69), (0xD8D48ADE), (0x1253AEDD), (0x80410FD2), (0x5B36889D), (0x880CE1E7), (0x56C498E7), (0xFA2BAAB2), (0xD4154F5C), (0x79516EED), (0xA42FDA7D), (0x278D2873), (0x19121535), (0x9C6EC6C7), (0x402340CB), (0x5A6A32B3), (0xE4345F27),
(0x8DC2D901), (0x02E7F88E), (0x7B3C6BEB), (0xE0946A4F), (0x9452886C), (0xFF2F6187), (0x2F8050F1), (0x2A217C61), (0x0AB21594), (0x801145C4), (0x1CF200A5), (0x82C544F1), (0xCF3C50B0), (0xD577FEF7), (0x60AD3C6E), (0x91AFF79E), (0x070723B3), (0x5870ECF6),
(0x64C224E2), (0x24FB1EC8), (0xC3481048), (0x151ABF3C), (0x136AC219), (0x4716CED1), (0xC8B8CE4E), (0x1D168210), (0x92334A81), (0x90DC910C), (0x49A00220), (0xE5F2C831), (0xB67DC785), (0x3C495174), (0x0D1F4C2A), (0x3AD787FD), (0xE9C9FB27), (0x7DFC2713),
(0xEB39C4B3), (0x96F75F18), (0x883CCC47), (0x91390BD1), (0x0EE25F6D), (0x6B688EF0), (0x2C8557DC), (0xE80C9172), (0xDE63DE06), (0x3E1B707B), (0xB2CF2E76), (0x490D1DF3), (0xCD1B51E1), (0xC228F0F9), (0xEEE2A00A), (0x081D834A), (0x1D2C1403), (0x083FE940),
(0x7614EDA0), (0x8282095A), (0xA7C08821), (0x4CD47D20), (0x32299191), (0x52A030E6), (0xDF7CB76E), (0x232ED44B), (0x8DAA150A), (0x1B593C51), (0x449CBA82), (0xABE6D3C4), (0xCCF6F5C3), (0x9064C1C2), (0x031FF59C), (0x4159FE4E), (0x421482C0), (0x20F6F00A),
(0xB866CA49), (0x75A66FAA), (0x5453515F), (0xB545D486), (0x885D9493), (0xEEC4EEB4), (0x88F9C893), (0xBBAA38B4), (0x5A57BA83), (0x12E848CC), (0x2FB62160), (0x840A1A54), (0x2A7AEDD6), (0x89AF2DBD), (0xC8BAAE2E), (0x56EEBD75), (0xE595B86D), (0xEEB123C6),
(0x3F94EBC1), (0x78CFBEE2), (0xCF901F11), (0x5A35A350), (0xDA22CB58), (0xBE4B8287), (0x25E078C0), (0x2BD4F0D3), (0x6A464B99), (0x4884B156), (0xF1FF7630), (0xC1328050), (0xD84C216C), (0x84A4D942), (0x2157A0A0), (0xF30A1412), (0x54AF6011), (0x00D70542),
(0xBDABE7DB), (0x1FF3534C), (0xD8672B4E), (0x7BE3BB3A), (0xC5595540), (0x161E1BFF), (0xD4372309), (0x7BC2ED52), (0xC1C636ED), (0x4DD32AA4), (0x86163246), (0x614EF688), (0x4EB44270), (0x88ED077C), (0x2F229466), (0x1A585201), (0x65BB2EE2), (0xDBADA0DD),
(0xE50995CB), (0xCA1E2760), (0x583FD81F), (0xE48D4859), (0xC2006152), (0x8F1F770C), (0x27CAA86F), (0x9610B845), (0xFC25BCE3), (0x37B8B39F), (0x10DCCDD1), (0xD57A4CAB), (0x7D543DE8), (0x6F45B958), (0x12F0B979), (0x818E79F9), (0xB4B2B01F), (0xF3035901),
(0x372F7BD9), (0x5D592E67), (0xDB5A552F), (0x6E61E625), (0x4E8A6A42), (0x2458ADB8), (0x5F12528C), (0x272B3ADE), (0xDE666A83), (0x7F18CFD7), (0x2F877DD8), (0xD73FFFB6), (0x79867956), (0x4E05FCAD), (0x19E78EEE), (0xE1BA6512), (0x99F6F091), (0x91690516),
(0x16AE4041), (0x04B96805), (0x7CA052FC), (0xA4D05EFD), (0x413D1F04), (0x53E91CC0), (0x6F87A082), (0x604FD202), (0x0B368D9F), (0xD5F22E5C), (0x2BE5A74E), (0x2F69EADE), (0x8BE537E1), (0x96975789), (0x3675A3A0), (0x0A272B71), (0x754B1D61), (0x98600581),
(0x659A2BE3), (0xB21BEF54), (0xF26351B0), (0x1C599573), (0x6B423B59), (0xEA371C96), (0xC9BDA499), (0xDDF25F37), (0xFBCAC4AD), (0x40CA788B), (0x13BC3981), (0x4176DA58), (0xD848DA74), (0x94740642), (0x54FD5479), (0x71502362), (0x25E81CF9), (0x53249562),
(0x52814827), (0x55DC5F2C), (0x8825BC50), (0x8E4D04D9), (0x8050F1FF), (0x212CC13E), (0x00C0D84C), (0x07711003), (0x406A5F45), (0xED0A98DE), (0x8A619731), (0x796A3B04), (0x7EAECF71), (0x7DF3DABE), (0x2B0FFB4A), (0xBA8A3FB8), (0x6202F8F2), (0x632C37B2),
(0x205271AA), (0xBA1C438C), (0xAA1C1182), (0xE6442A65), (0xD736E043), (0x95715C5C), (0x3D604C28), (0xD21D0D74), (0xA1FB0C21), (0xCE8F065D), (0xB8B36463), (0x7F788C33), (0x84AB53A5), (0x4B33AF46), (0x40C2736A), (0x0D6571B7), (0xFC8D8292), (0x9D1C5182),
(0xF7B670E2), (0xBB2B9E91), (0x137851D5), (0xBCF87CA8), (0xE082A7A6), (0x502404E5), (0x5025A6E0), (0x25494EA6), (0x5A6A8A14), (0xE301A689), (0x70CBEE6B), (0xC80644C1), (0xE9914CE6), (0x9481F1E6), (0xB9E4DA34), (0x584B1ED8), (0xACC39648), (0x31EFCD62),
(0x07C82AE9), (0xEE9D90E5), (0x59BAEC75), (0x40178B0E), (0x9FDC1C9C), (0xF3A6488F), (0x4B6C1367), (0x9F7DF109), (0xCBDF8EDF), (0xBA76ED33), (0xF15DBB9F), (0xD48A197F), (0xF11E75EE), (0xA76B479B), (0x706DE78B), (0x6559AC65), (0xC013C182), (0x461E93C7),
(0xA989EAF1), (0x59448C00), (0x721FAC38), (0x546DEA79), (0x223F67C0), (0x5434647C), (0xB3EF4F0A), (0xD4FAF1F1), (0x0BD4E38A), (0xFF61527E), (0xD6EA5DD2), (0x644C7105), (0x31AE2189), (0x11EC0A1A), (0x18B3AD80), (0x47D0E80A), (0x773D8BB2), (0xEFA6747E),
(0xF23DE8B0), (0x026BB1E7), (0x03C14060), (0xC1997DF7), (0x95986B9C), (0x8A5D6170), (0x54DB3A56), (0x22961D41), (0x46813471), (0xABED16A4), (0xD1F83E8C), (0x2594ACAC), (0x1D5560EA), (0x0432EFF4), (0xA389D7E8), (0xAFE92C62), (0xFBBAF1E7), (0x42D0D0A1),
(0xA2C3ECB2), (0x603DD635), (0xEBBC5915), (0x5B0C7CE0), (0x8D03640C), (0x22E298DB), (0x38CE3BDC), (0x21E6B6B4), (0xC9742C3F), (0x4BDF7448), (0xBAD09D00), (0xED6A8397), (0xF6F8001A), (0x31E3591E), (0xAEC37EFE), (0x22D9AD06), (0x5E254AEC), (0xD852D32E),
(0xE1C968AE), (0xA3C3C265), (0xD67BD78A), (0xF1FF9D8C), (0xC03A8050), (0x847A21FC), (0x810EDA5D), (0x63808951), (0xC8FE9E9E), (0xFB6DD3E5), (0x77BD7A03), (0x6DC14677), (0xFF81D831), (0x43201100), (0xA78E16FE), (0x393CD0A0), (0x514F6DDD), (0x427BA4BC),
(0xEBE7A676), (0xB771639B), (0x8DABB9E7), (0x812FA5AD), (0x3F102120), (0x60E42454), (0xEFFBFFC5), (0xBEB1D5DD), (0x7A59DDFD), (0xE8968D7A), (0x8AAD3462), (0xAB65327D), (0x0CF2ECFB), (0xFA3249A4), (0x97FC3A42), (0x314A7089), (0x2991E6DC), (0xA98693C4),
(0x65EB2011), (0xCB4D8594), (0x1AAFF14E), (0x1D0D0492), (0x2E5A784D), (0xAE4EC738), (0xF16D667A), (0xB0C26030), (0xADA3DFC9), (0x4ED874E5), (0xD441A6AE), (0x19E3AC05), (0xC6B865DC), (0xE8E4285A), (0x99248C2B), (0xC2981122), (0x1B014288), (0x442A0580),
(0x84A0A1C1), (0x216F0248), (0x202E5602), (0xEAEBAACA), (0xDD5ABEE7), (0x8B9C4B43), (0x5D5D46D7), (0x8FBE7C1D), (0x6F9CAB87), (0xB0AB0AAE), (0x98BC7936), (0xF251B911), (0x4469F2D1), (0xA9099088), (0xD219BD66), (0x7490AE22), (0xBC0A9820), (0x119D0136),
(0x8592661C), (0x260C0D18), (0xB9A1C5B1), (0xA68591E4), (0xFCBD4F29), (0x5965A3C7), (0x7D1ED664), (0x9BDDEEE3), (0x92237D5A), (0xB7332DA6), (0xEA5632E5), (0x9BFE1624), (0x6A2BB18A), (0x8C0DD72C), (0x4F319F56), (0xEE8B5B03), (0x60967BC8), (0xB4918F75),
(0xF295D5F6), (0x652AD80D), (0x0CCE9552), (0x0BCAA0A6), (0x3CF2D28E), (0x1853956E), (0x7B38DEAA), (0xEAAB970B), (0x64086D1F), (0x3E7645DD), (0x986F1342), (0xBBB0E188), (0x7D6D5642), (0x6821827D), (0xFCBC4729), (0x3D114FB9), (0x2D2542E5), (0x654CC1F0),
(0x04F1B75E), (0x290B54AF), (0x7D79AB1A), (0xF34E31A2), (0x73A9F300), (0x7ABCA89E), (0xB56DA974), (0xCACDB07C), (0x4EE1ADD4), (0xEF859B34), (0x89F47B47), (0x515C155A), (0x38D06757), (0x8050F1FF), (0x21C8E03A), (0xB155841A), (0x30716685), (0x0A2B48AC),
(0x7041C40A), (0x104C44D0), (0x1F51B618), (0xF0D4F39B), (0xFCAEBA52), (0x30EA62CD), (0xA294B772), (0x221E0417), (0xAA8EF192), (0x3E568C78), (0xD386EF9A), (0x13B54FB1), (0x84818EC3), (0xB87669F8), (0xCD90565A), (0x73D7C876), (0xD6191512), (0xAC228704),
(0xA7785D48), (0x17CF34DD), (0x8AF5AB40), (0x365C6AFA), (0x17DFD59E), (0xB924C26E), (0xE9DE5844), (0x3DCDDCD9), (0xCDA2DFB4), (0x8A5B534E), (0xA2D9DACA), (0x11679569), (0xAEF6E7E9), (0xC9D2417C), (0x515DC9C3), (0x157A5B12), (0x4C960DD6), (0xD413CC01),
(0x65319545), (0x6CF1309D), (0x000338C7), (0x460AA7FC), (0xC1FA4440), (0x0E3AE048), (0xF4B212B5), (0xC7895D2B), (0x676AFAF3), (0xEF404F75), (0xBFCDB17C), (0xA37FE6C6), (0x20123DD5), (0xEE96BD3B), (0x3EB8A62E), (0x068845E1), (0xC4960216), (0x7BB11D8D),
(0x2D657467), (0xFC6FACC5), (0xC21AAD31), (0x642745F4), (0x4507E139), (0xF7369DC9), (0x6198ADEB), (0x63B98B10), (0xDEE585AB), (0xA38EFFAC), (0x30AE1E41), (0x820667A9), (0x6B77A007), (0x240040A7), (0xB77A7D95), (0x208893CA), (0x085B4610), (0x14234854),
(0xAC022786), (0x2D57346F), (0xB64A1A67), (0x605D45B2), (0xE051C001), (0x9176C11D), (0x23BBE945), (0x2C03E26A), (0xC2811BAD), (0x1DD814D4), (0x3C674B68), (0x28EC33C7), (0xD9EDE5E4), (0x5C46C248), (0x4098898F), (0x5BA96280), (0xEFF77B91), (0x3332CA5B),
(0x661CAF6B), (0x6E0D564E), (0xB70D0863), (0x240285C0), (0xBBBD3EA7), (0xCA2E568D), (0xD1D3F612), (0xC916115E), (0xB7FC0A6C), (0x1961AE9A), (0xB5DF3965), (0xC1B95065), (0x89E29665), (0x3F29FBED), (0x3F1BED73), (0xC155BE1D), (0x7256AE4C), (0x4A59797B),
(0x6009D13B), (0x91850385), (0xE4545920), (0x15ECCDA1), (0xFFC7BA20), (0x2B8050F1), (0x1A21D4C0), (0x3008BE89), (0x6C0FF317), (0x7444D852), (0x05841411), (0x84C80022), (0x07C630E0), (0x7E6F8FA3), (0xF3BDF3C7), (0x4C55DFF7), (0xE0B1C8D1), (0xC3F5CF4D),
(0x1C22A836), (0xB29D3315), (0xD4966541), (0x45C85800), (0xAD8DBD46), (0x2C19CFAA), (0x540FA674), (0xC3C913D8), (0xD2183753), (0xCB9FCF26), (0x8346FC89), (0xA3408078), (0xCFD84CC1), (0xF580F035), (0x7FD067A7), (0x87E7FCB8), (0x09C09406), (0xDDBB3131),
(0xFF79FFE1), (0xEAFBEEF1), (0xB660BCE8), (0x99899C31), (0x9FEB22C1), (0xC75EF68F), (0x13800B99), (0xCD7F3100), (0x09502ADC), (0x101E00E8), (0x406CB945), (0x820A1B2B), (0x68A0B082), (0x0C1B1864), (0xB3690354), (0x63C5B5A3), (0x535EB4C8), (0xF7D86918),
(0xD1BA58FC), (0xF42910FA), (0xFBDB1929), (0xC7C0C2A4), (0xD2074752), (0xB96803B4), (0x84065E38), (0x819AF48E), (0xE9864FF3), (0xA6E41D0E), (0x50E7442B), (0xC7574D7B), (0x196B6AB3), (0x041D5854), (0x629162D3), (0x042713E0), (0x9AE31297), (0x4B2F4E36),
(0xCB8CE0E0), (0xB2F0682A), (0xAB33D6F5), (0x2F53D82E), (0x66A43815), (0x4433AAA8), (0x82EA0ED5), (0x457876F2), (0xD30BD36C), (0x8D49D28C), (0x0E68260E), (0xD056512C), (0x7B86DB11), (0x64633661), (0x8265FF25), (0x6D2B7148), (0x529DA0D8), (0xBC180017),
(0x19AB803B), (0x83F00020), (0x50F1FF07), (0xD4C02D80), (0xFC881A21), (0xC1BF0100), (0x5841680B), (0x103B2CC0), (0xB042C28A), (0x2324E8A8), (0x31531430), (0xEF71DD0E), (0x7C1B9D8E), (0x0998C228), (0xD9EC6548), (0x8EEF8448), (0x0C561CAF), (0x4BA39DFD),
(0x58947FE2), (0x959B12A2), (0x6D52EAEC), (0x1D95DDD5), (0x310C222B), (0x63634B18), (0x4DE6C5B8), (0x526C9106), (0x49B5E8CC), (0xC3C33004), (0x605CD287), (0x09A6390C), (0x350C33BC), (0x953050CC), (0x960552A3), (0x54F01186), (0x0E8D7CFE), (0x459A0022),
(0xC3576966), (0xB0D6F6E8), (0xB8F13AAC), (0xE440034C), (0x36118851), (0xF51BE727), (0x2E1689A0), (0xE82CCF55), (0x6DF4E3CB), (0xEA665663), (0xC2E53D6F), (0x9DC1F06B), (0xFCC2E878), (0x4292D162), (0x4840C433), (0xB618DB43), (0x06092C15), (0x500134C1),
(0xA6DB8955), (0xD5E8A5BB), (0x142B7829), (0x75F42939), (0x0EBF4D10), (0x1A2B078C), (0x7C2A5A46), (0x3EA1B9A7), (0x31AAC898), (0x1327B852), (0x277C65A1), (0x5280CE27), (0xFF512213), (0x39B47EBF), (0x154CA000), (0x106E7DA2), (0xAFA4C925), (0xC48CE379),
(0xAB2E4573), (0x5E44B8FD), (0xECD2A424), (0x8C5300AA), (0xE425A180), (0x6D5D0D3B), (0xA70540A8), (0xF8ECF432), (0x884696B0), (0xB79755BF), (0x62CAD419), (0x4BBB38B6), (0xCD5C79D7), (0x82D4E29B), (0x6DD48E91), (0xA128A3A9), (0xF2ED6726), (0x339A63F6),
(0x35D6F4EA), (0x8DC8B491), (0xCA04D09C), (0xFF074FD0), (0x2D8050F1), (0x1A21D880), (0x1900FE88), (0x6C09D1BF), (0x56483001), (0xC20A1A29), (0x449180A3), (0x028C5160), (0x75FAECDD), (0x6250705B), (0x89EA010A), (0x7B9F9851), (0x9D6C5994), (0x8602C3E4),
(0x03D0E863), (0x04322251), (0x116095B7), (0x789276CA), (0x00000000), (0x00000978), (0x8000C688), (0x8000BCD8), (0x80010000), (0x9FC3F7B8), (0xDE47BC1B), (0xE60FB4F8), (0xEA27469D), (0xCCA1E66D), (0x8FFF7010), (0x8000BCD8), (0x000000D0), (0x8000B4B0),
(0x8FFFFB24), (0x9FC3F7B8), (0x00000000), (0x8000B418), (0x8FFF7010), (0x8000C688), (0x000000D0), (0x9FC3FD3C), (0xD625F8C0), (0x478D832A), (0xF693D922), (0x3617D816), (0x80000648), (0x00000000), (0x00000003), (0x00000012), (0x9FC49964), (0x46141924),
(0x4D834205), (0x16B799A6), (0xA39494A6), (0xCCF23C60), (0x00000001), (0x9FC47468), (0x00000000), (0x00000072), (0x00008001), (0x8FFFFB58), (0x00000003), (0x402C7413), (0x00000016), (0x402C7413), (0x9FC48498), (0x00000000), (0x00000002), (0x00000001),
(0x00000072), (0x9FC4783C), (0x8000C690), (0x0358CDD5), (0x00000000), (0x00008001), (0x00000072), (0x80000648), (0x00000000), (0x00008001), (0x8000BD40), (0x1A19F8D8), (0x00008001), (0x800003C0), (0x00000003), (0x9FC428CC), (0xF6192015), (0xFBB77F92),
(0x8F2C8BFC), (0x3C391923), (0x4014428C), (0x140045AE), (0xFFFD2BB6), (0x2D8050F1), (0x8000BD40), (0x00000003), (0x00000000), (0x2CC05811), (0x00008001), (0x800003C0), (0x80000648), (0x80000648), (0x9FC4206C), (0x8FFFFD78), (0x8FFFFEEC), (0x8000C668),
(0x80000648), (0x8FFFFD78), (0x00000000), (0x00003040), (0x8000FFC0), (0x8000BCD8), (0x80010000), (0x9FC3F7B8), (0x00000000), (0x00002CC0), (0x80013340), (0x8000BCD8), (0x80010FD0), (0x80012548)
};

//static const INT __twiddles_mips_fft32_2048[] =
//{
//(0x7FFFFFFF), (0x00000000), (0x7FFFD884), (0xFF9B781D), (0x7FFF6215), (0xFF36F078), (0x7FFE9CB1), (0xFED26950), (0x7FFD8859), (0xFE6DE2E0), (0x7FFC250E), (0xFE095D6A), (0x7FFA72D0), (0xFDA4D929), (0x7FF871A0), (0xFD40565D), (0x7FF62181), (0xFCDBD540),
//(0x7FF38272), (0xFC775617), (0x7FF09476), (0xFC12D91B), (0x7FED578F), (0xFBAE5E8C), (0x7FE9CBBE), (0xFB49E6A3), (0x7FE5F107), (0xFAE571A2), (0x7FE1C76A), (0xFA80FFCE), (0x7FDD4EEB), (0xFA1C9157), (0x7FD8878C), (0xF9B82681), (0x7FD37151), (0xF953BF94),
//(0x7FCE0C3D), (0xF8EF5CBC), (0x7FC85852), (0xF88AFE3F), (0x7FC25595), (0xF826A464), (0x7FBC0409), (0xF7C24F61), (0x7FB563B2), (0xF75DFF6B), (0x7FAE7493), (0xF6F9B4C8), (0x7FA736B2), (0xF6956FB7), (0x7F9FAA13), (0xF6313074), (0x7F97CEBB), (0xF5CCF73E),
//(0x7F8FA4AF), (0xF568C463), (0x7F872BF2), (0xF5049800), (0x7F7E648B), (0xF4A07264), (0x7F754E7E), (0xF43C53CB), (0x7F6BE9D3), (0xF3D83C74), (0x7F62368D), (0xF3742C9D), (0x7F5834B6), (0xF3102492), (0x7F4DE450), (0xF2AC2473), (0x7F434562), (0xF2482C8D),
//(0x7F3857F4), (0xF1E43D1C), (0x7F2D1C0C), (0xF1805660), (0x7F2191B2), (0xF11C7895), (0x7F15B8ED), (0xF0B8A409), (0x7F0991C3), (0xF054D8DA), (0x7EFD1C3B), (0xEFF11755), (0x7EF05860), (0xEF8D5FC8), (0x7EE34634), (0xEF29B241), (0x7ED5E5C6), (0xEEC60F3C),
//(0x7EC83717), (0xEE6276B7), (0x7EBA3A38), (0xEDFEE930), (0x7EABEF29), (0xED9B66A5), (0x7E9D55FB), (0xED37EF91), (0x7E8E6EB2), (0xECD48414), (0x7E7F3954), (0xEC71244A), (0x7E6FB5F4), (0xEC0DD0B0), (0x7E5FE490), (0xEBAA8944), (0x7E4FC53D), (0xEB474E83),
//(0x7E3F5800), (0xEAE4208A), (0x7E2E9CDD), (0xEA80FF77), (0x7E1D93EA), (0xEA1DEBC6), (0x7E0C3D26), (0xE9BAE575), (0x7DFA98A7), (0xE957ED00), (0x7DE8A66C), (0xE8F50266), (0x7DD6668D), (0xE8922621), (0x7DC3D90E), (0xE82F5851), (0x7DB0FDF5), (0xE7CC9912),
//(0x7D9DD55A), (0xE769E8E0), (0x7D8A5F3C), (0xE70747B9), (0x7D769BB4), (0xE6A4B619), (0x7D628AC7), (0xE642341C), (0x7D4E2C7C), (0xE5DFC1E2), (0x7D3980ED), (0xE57D5FE4), (0x7D248818), (0xE51B0E22), (0x7D0F4217), (0xE4B8CD16), (0x7CF9AEEC), (0xE4569CBE),
//(0x7CE3CEB0), (0xE3F47D95), (0x7CCDA16A), (0xE3926FB9), (0x7CB72721), (0xE3307347), (0x7CA05FF1), (0xE2CE88BA), (0x7C894BDA), (0xE26CB010), (0x7C71EAF7), (0xE20AE9C3), (0x7C5A3D52), (0xE1A935F1), (0x7C4242F0), (0xE14794B7), (0x7C29FBEF), (0xE0E6068F),
//(0x7C11684F), (0xE0848B77), (0x7BF88830), (0xE02323EA), (0x7BDF5B8F), (0xDFC1CFE5), (0x7BC5E296), (0xDF609002), (0x7BAC1D33), (0xDEFF6400), (0x7B920B86), (0xDE9E4C5B), (0x7B77ADA0), (0xDE3D494C), (0x7B5D03A1), (0xDDDC5B4F), (0x7B420D79), (0xDD7B8222),
//(0x7B26CB49), (0xDD1ABE41), (0x7B0B3D33), (0xDCBA1024), (0x7AEF6325), (0xDC59778B), (0x7AD33D41), (0xDBF8F4F0), (0x7AB6CB9A), (0xDB98888E), (0x7A9A0E53), (0xDB3832DE), (0x7A7D0559), (0xDAD7F3A2), (0x7A5FB0D1), (0xDA77CB50), (0x7A4210DE), (0xDA17BA63),
//(0x7A24256F), (0xD9B7C09A), (0x7A05EEA8), (0xD957DE6F), (0x79E76C9C), (0xD8F8141D), (0x79C89F71), (0xD898621A), (0x79A98713), (0xD838C82A), (0x798A23A8), (0xD7D946C3), (0x796A755A), (0xD779DE5D), (0x794A7C11), (0xD71A8EBA), (0x792A37F8), (0xD6BB5850),
//(0x7909A935), (0xD65C3B98), (0x78E8CFB4), (0xD5FD3854), (0x78C7AB9E), (0xD59E4EF9), (0x78A63D07), (0xD53F7FC3), (0x78848419), (0xD4E0CB28), (0x786280BE), (0xD48230EA), (0x78403321), (0xD423B181), (0x781D9B6D), (0xD3C54D62), (0x77FAB98A), (0xD367044F),
//(0x77D78DA6), (0xD308D6BE), (0x77B417D4), (0xD2AAC4EB), (0x77905842), (0xD24CCF49), (0x776C4ED9), (0xD1EEF59D), (0x7747FBC5), (0xD191385B), (0x77235F35), (0xD13397FA), (0x76FE790F), (0xD0D6143A), (0x76D94983), (0xD078AD93), (0x76B3D0A6), (0xD01B643D),
//(0x768E0EA9), (0xCFBE38AD), (0x76680373), (0xCF612AA7), (0x7641AF32), (0xCF043A9E), (0x761B1218), (0xCEA76907), (0x75F42C0B), (0xCE4AB5A6), (0x75CCFD3B), (0xCDEE20EF), (0x75A585D9), (0xCD91AB55), (0x757DC5CD), (0xCD35549C), (0x7555BD47), (0xCCD91D37),
//(0x752D6C60), (0xCC7D0561), (0x7504D34C), (0xCC210D8B), (0x74DBF1EE), (0xCBC5357A), (0x74B2C87B), (0xCB697DA0), (0x74895725), (0xCB0DE672), (0x745F9DD3), (0xCAB26FB2), (0x74359CB8), (0xCA5719D3), (0x740B53ED), (0xC9FBE50E), (0x73E0C3A9), (0xC9A0D1D4),
//(0x73B5EBCF), (0xC945DFEB), (0x738ACC94), (0xC8EB0FC4), (0x735F662F), (0xC89061D1), (0x7333B884), (0xC835D5D6), (0x7307C3C9), (0xC7DB6C45), (0x72DB8819), (0xC7812556), (0x72AF05AB), (0xC727017A), (0x72823C63), (0xC6CD0075), (0x72552C79), (0xC67322B9),
//(0x7227D624), (0xC61968B6), (0x71FA3948), (0xC5BFD232), (0x71CC561E), (0xC5665F9B), (0x719E2CDE), (0xC50D1164), (0x716FBD6C), (0xC4B3E751), (0x71410800), (0xC45AE1D1), (0x71120CB7), (0xC402011C), (0x70E2CBCD), (0xC3A945A1), (0x70B34523), (0xC350AF26),
//(0x708378F4), (0xC2F83E1B), (0x7053677C), (0xC29FF2EC), (0x7023109C), (0xC247CD62), (0x6FF27490), (0xC1EFCDEA), (0x6FC19376), (0xC197F4BB), (0x6F906D6B), (0xC140420A), (0x6F5F02CF), (0xC0E8B67E), (0x6F2D5340), (0xC091516E), (0x6EFB5F1D), (0xC03A137E),
//(0x6EC92684), (0xBFE2FCE5), (0x6E96A995), (0xBF8C0DD8), (0x6E63E86E), (0xBF35468E), (0x6E30E32F), (0xBEDEA73A), (0x6DFD9A38), (0xBE883082), (0x6DCA0D27), (0xBE31E1BF), (0x6D963C5D), (0xBDDBBB92), (0x6D6227FA), (0xBD85BE33), (0x6D2DD01E), (0xBD2FE9D5),
//(0x6CF934E8), (0xBCDA3EAE), (0x6CC4567A), (0xBC84BCF2), (0x6C8F3538), (0xBC2F6544), (0x6C59D0BC), (0xBBDA36FD), (0x6C242969), (0xBB8532C0), (0x6BEE3F61), (0xBB3058C1), (0x6BB812C5), (0xBADBA934), (0x6B81A3B7), (0xBA87244E), (0x6B4AF258), (0xBA32CA42),
//(0x6B13FF11), (0xB9DE9BB0), (0x6ADCC976), (0xB98A97F6), (0x6AA551F0), (0xB936BFB2), (0x6A6D98A1), (0xB8E31318), (0x6A359DAC), (0xB88F925B), (0x69FD6132), (0xB83C3DB1), (0x69C4E358), (0xB7E9154A), (0x698C2487), (0xB79619C6), (0x69532453), (0xB7434A82),
//(0x6919E326), (0xB6F0A81D), (0x68E06125), (0xB69E32C9), (0x68A69E72), (0xB64BEAB9), (0x686C9B31), (0xB5F9D021), (0x68325786), (0xB5A7E331), (0x67F7D3DF), (0xB5562486), (0x67BD0FCC), (0xB5049380), (0x67820BBB), (0xB4B330BC), (0x6746C7D1), (0xB461FC6A),
//(0x670B4432), (0xB410F6BE), (0x66CF8103), (0xB3C01FE9), (0x66937EB5), (0xB36F7883), (0x66573CD5), (0xB31EFFF0), (0x661ABBD3), (0xB2CEB6CB), (0x65DDFBD6), (0xB27E9D43), (0x65A0FD03), (0xB22EB38A), (0x6563BF7E), (0xB1DEF9D2), (0x6526436F), (0xB18F704B),
//(0x64E8894A), (0xB140178C), (0x64AA9098), (0xB0F0EEFC), (0x646C59CC), (0xB0A1F730), (0x642DE50F), (0xB053305A), (0x63EF3286), (0xB0049AA9), (0x63B04257), (0xAFB6364E), (0x637114AB), (0xAF68037B), (0x6331A9F8), (0xAF1A02C1), (0x62F201C4), (0xAECC338B),
//(0x62B21C87), (0xAE7E966C), (0x6271FA69), (0xAE312B94), (0x62319B91), (0xADE3F333), (0x61F10027), (0xAD96ED77), (0x61B02852), (0xAD4A1A92), (0x616F148E), (0xACFD7B13), (0x612DC45D), (0xACB10E67), (0x60EC383A), (0xAC64D51F), (0x60AA704E), (0xAC18CF69),
//(0x60686CC0), (0xABCCFD75), (0x60262DBB), (0xAB815F71), (0x5FE3B366), (0xAB35F58C), (0x5FA0FE40), (0xAAEAC054), (0x5F5E0DC8), (0xAA9FBF38), (0x5F1AE27C), (0xAA54F2C6), (0x5ED77C86), (0xAA0A5B2C), (0x5E93DC0E), (0xA9BFF899), (0x5E500140), (0xA975CB39),
//(0x5E0BEC44), (0xA92BD33C), (0x5DC79D9C), (0xA8E2112C), (0x5D8314C4), (0xA898847B), (0x5D3E523E), (0xA84F2DB4), (0x5CF95632), (0xA8060D05), (0x5CB420CD), (0xA7BD229A), (0x5C6EB238), (0xA7746EA2), (0x5C290AA0), (0xA72BF148), (0x5BE32A86), (0xA6E3AB15),
//(0x5B9D1166), (0xA69B9B7D), (0x5B56BFC2), (0xA653C30A), (0x5B1035C7), (0xA60C21E8), (0x5AC973A0), (0xA5C4B843), (0x5A827978), (0xA57D8646), (0x5A3B47D6), (0xA5368C78), (0x59F3DE30), (0xA4EFCA51), (0x59AC3D0E), (0xA4A94056), (0x5964649B), (0xA462EEB2),
//(0x591C5504), (0xA41CD591), (0x58D40E75), (0xA3D6F51F), (0x588B911B), (0xA3914D87), (0x5842DD7E), (0xA34BDF4B), (0x57F9F314), (0xA306A9E5), (0x57B0D265), (0xA2C1ADDA), (0x57677B9E), (0xA27CEB53), (0x571DEEED), (0xA238627B), (0x56D42C7F), (0xA1F4137C),
//(0x568A3482), (0xA1AFFE81), (0x56400781), (0xA16C2409), (0x55F5A4ED), (0xA1288391), (0x55AB0D54), (0xA0E51D9B), (0x556040E2), (0xA0A1F24F), (0x55153FC6), (0xA05F01D7), (0x54CA0A2E), (0xA01C4C5C), (0x547EA049), (0x9FD9D207), (0x543302A5), (0x9F979356),
//(0x53E730B1), (0x9F558FC9), (0x539B2AFB), (0x9F13C7DC), (0x534EF1B3), (0x9ED23BB9), (0x53028507), (0x9E90EB88), (0x52B5E526), (0x9E4FD771), (0x52691241), (0x9E0EFF9D), (0x521C0CE8), (0x9DCE6485), (0x51CED486), (0x9D8E05AD), (0x518169AE), (0x9D4DE38F),
//(0x5133CC8F), (0x9D0DFE52), (0x50E5FD59), (0x9CCE561E), (0x5097FC3C), (0x9C8EEB1A), (0x5049C968), (0x9C4FBD6E), (0x4FFB6572), (0x9C10CD90), (0x4FACCFC1), (0x9BD21B06), (0x4F5E08EB), (0x9B93A649), (0x4F0F111F), (0x9B556F7E), (0x4EBFE88F), (0x9B1776CB),
//(0x4E708F6B), (0x9AD9BC57), (0x4E2105E4), (0x9A9C4048), (0x4DD14C91), (0x9A5F0312), (0x4D8162D8), (0x9A22043F), (0x4D314951), (0x99E54441), (0x4CE1002B), (0x99A8C340), (0x4C908799), (0x996C815F), (0x4C3FDFCC), (0x99307EC5), (0x4BEF095E), (0x98F4BBE2),
//(0x4B9E03B1), (0x98B93843), (0x4B4CCF60), (0x987DF459), (0x4AFB6C9B), (0x9842F048), (0x4AA9DB96), (0x98082C35), (0x4A581C83), (0x97CDA844), (0x4A062F93), (0x97936499), (0x49B41562), (0x975961A2), (0x4961CD53), (0x971F9EEF), (0x490F57FF), (0x96E61CED),
//(0x48BCB59A), (0x96ACDBC1), (0x4869E657), (0x9673DB8C), (0x4816EA68), (0x963B1C73), (0x47C3C202), (0x96029E99), (0x47706DC1), (0x95CA6267), (0x471CED05), (0x95926772), (0x46C9406B), (0x955AAE23), (0x46756827), (0x9523369D), (0x4621646C), (0x94EC0102),
//(0x45CD356F), (0x94B50D74), (0x4578DB63), (0x947E5C16), (0x452456E9), (0x9447ED4D), (0x44CFA75C), (0x9411C0B1), (0x447ACD5D), (0x93DBD6AA), (0x4425C920), (0x93A62F57), (0x43D09AD9), (0x9370CADA), (0x437B42BE), (0x933BA954), (0x4325C102), (0x9306CAE7),
//(0x42D015DB), (0x92D22FB1), (0x427A417D), (0x929DD7D5), (0x4224441D), (0x9269C372), (0x41CE1ECD), (0x9235F32C), (0x4177D009), (0x9202661B), (0x412158E3), (0x91CF1CE3), (0x40CAB990), (0x919C17A4), (0x4073F245), (0x9169567D), (0x401D0339), (0x9136D98D),
//(0x3FC5ECA0), (0x9104A0F4), (0x3F6EAEB0), (0x90D2ACD1), (0x3F17499F), (0x90A0FD42), (0x3EBFBDA4), (0x906F9267), (0x3E680AF3), (0x903E6C5D), (0x3E1031C4), (0x900D8B43), (0x3DB8324C), (0x8FDCEF37), (0x3D600CC1), (0x8FAC9857), (0x3D07C23C), (0x8F7C873A),
//(0x3CAF5130), (0x8F4CBB0B), (0x3C56BAB5), (0x8F1D3461), (0x3BFDFF02), (0x8EEDF359), (0x3BA51E4D), (0x8EBEF810), (0x3B4C18CE), (0x8E9042A4), (0x3AF2EEBA), (0x8E61D331), (0x3A99A04A), (0x8E33A9D5), (0x3A402DB4), (0x8E05C6AA), (0x39E6972F), (0x8DD829CE),
//(0x398CDCF3), (0x8DAAD35D), (0x3932FF37), (0x8D7DC373), (0x38D8FE32), (0x8D50FA2B), (0x387EDA1C), (0x8D2477A2), (0x38249413), (0x8CF83C62), (0x37CA2A82), (0x8CCC47A7), (0x376F9E88), (0x8CA099FB), (0x3714F05B), (0x8C75337B), (0x36BA2034), (0x8C4A1440),
//(0x365F2E4B), (0x8C1F3C66), (0x36041AD7), (0x8BF4AC06), (0x35A8E612), (0x8BCA633C), (0x354D9033), (0x8BA06221), (0x34F21973), (0x8B76A8CE), (0x3496820A), (0x8B4D375F), (0x343ACA30), (0x8B240DEC), (0x33DEF21F), (0x8AFB2C8E), (0x3382FAF9), (0x8AD293C7),
//(0x3326E323), (0x8AAA42E0), (0x32CAABBE), (0x8A823A5A), (0x326E5505), (0x8A5A7A4D), (0x3211DF31), (0x8A3302D3), (0x31B54A79), (0x8A0BD403), (0x31589718), (0x89E4EDF5), (0x30FBC547), (0x89BE50C2), (0x309ED53E), (0x8997FC82), (0x3041C737), (0x8971F14B),
//(0x2FE49B6C), (0x894C2F36), (0x2F875216), (0x8926B65A), (0x2F29EB6E), (0x890186CE), (0x2ECC67AF), (0x88DCA0A9), (0x2E6EC800), (0x88B8045E), (0x2E110ABF), (0x8893B14A), (0x2DB33112), (0x886FA7E1), (0x2D553B35), (0x884BE838), (0x2CF72962), (0x88287266),
//(0x2C98FBD1), (0x88054682), (0x2C3AB2BF), (0x87E2649F), (0x2BDC4E63), (0x87BFCCD5), (0x2B7DCEFA), (0x879D7F38), (0x2B1F34BC), (0x877B7BDD), (0x2AC07FE5), (0x8759C2DA), (0x2A61B0AF), (0x87385443), (0x2A02C754), (0x8717302D), (0x29A3C40F), (0x86F656AC),
//(0x2944A80D), (0x86D5C828), (0x28E571A3), (0x86B5840E), (0x28862200), (0x86958AC5), (0x2826B95E), (0x8675DC62), (0x27C737F7), (0x865678F8), (0x27679E06), (0x8637609A), (0x2707EBC7), (0x8618935B), (0x26A82174), (0x85FA114F), (0x26483F49), (0x85DBDA88),
//(0x25E84581), (0x85BDEF19), (0x25883456), (0x85A04F14), (0x25280C05), (0x8582FA8C), (0x24C7CCC8), (0x8565F192), (0x246777D0), (0x85493482), (0x24070B6E), (0x852CC2DA), (0x23A688D3), (0x85109CF7), (0x2345F03B), (0x84F4C2E8), (0x22E541E0), (0x84D934C0),
//(0x22847DFF), (0x84BDF290), (0x2223A4D2), (0x84A2FC68), (0x21C2B697), (0x84885258), (0x2161B389), (0x846DF472), (0x21009BE3), (0x8453E2C5), (0x209F6FE1), (0x843A1D62), (0x203E2FC0), (0x8420A459), (0x1FDCDBBB), (0x840777B9), (0x1F7B740F), (0x83EE9792),
//(0x1F19F9F0), (0x83D60431), (0x1EB86BA9), (0x83BDBD28), (0x1E56CA6E), (0x83A5C2C5), (0x1DF5167D), (0x838E1518), (0x1D935011), (0x8376B42E), (0x1D317767), (0x835FA017), (0x1CCF8CBB), (0x8348D8DF), (0x1C6D9049), (0x83325E96), (0x1C0B824E), (0x831C314A),
//(0x1BA96306), (0x83065107), (0x1B4732AE), (0x82F0BDDB), (0x1AE4F182), (0x82DB77D5), (0x1A829FC0), (0x82C67F00), (0x1A203DA3), (0x82B1D36A), (0x19BDCC63), (0x829D7553), (0x195B4A48), (0x82896460), (0x18F8B888), (0x8275A0D1), (0x18961761), (0x82622AB3),
//(0x18336710), (0x824F0211), (0x17D0A7D1), (0x823C26F8), (0x176DD9E1), (0x82299974), (0x170AFD7D), (0x8217598E), (0x16A812E3), (0x82056754), (0x16451A4E), (0x81F3C2CF), (0x15E213FD), (0x81E26C0B), (0x157F002D), (0x81D16313), (0x151BDF19), (0x81C0A7F1),
//(0x14B8B101), (0x81B03AAF), (0x1455771D), (0x81A01B80), (0x13F22FB1), (0x81904A1C), (0x138EDBF8), (0x8180C6B6), (0x132B7C2E), (0x81719158), (0x12C81090), (0x8162AA0A), (0x1264995D), (0x815410D8), (0x120116D2), (0x8145C5C8), (0x119D892B), (0x8137C8E5),
//(0x1139F0A7), (0x812A1A36), (0x10D64D82), (0x811CB9C4), (0x10729FFB), (0x810FA798), (0x100EE84E), (0x8102E3B9), (0x0FAB26B9), (0x80F66E30), (0x0F475C78), (0x80EA4722), (0x0EE387CD), (0x80DE6E5A), (0x0E7FA9F2), (0x80D2E3FD), (0x0E1BC326), (0x80C7A813),
//(0x0DB7D3A5), (0x80BCBAA3), (0x0D53DBAF), (0x80B21BB4), (0x0CEFDB80), (0x80A7CB4C), (0x0C8BD356), (0x809DC971), (0x0C27C36E), (0x8094162B), (0x0BC3AC07), (0x808AB17E), (0x0B5F8D5F), (0x80819B70), (0x0AFB67B2), (0x8078D407), (0x0A973B3F), (0x80705B49),
//(0x0A330844), (0x8068313B), (0x09CECFFE), (0x806055F5), (0x096A90AB), (0x8058C955), (0x09064B8A), (0x80518B72), (0x08A200D7), (0x804A9C53), (0x083DB0D1), (0x8043FBFA), (0x07D95BB6), (0x803DAA6D), (0x077501C3), (0x8037A7AE), (0x0710A337), (0x8031F3C3),
//(0x06AC404F), (0x802C8EAD), (0x0647D949), (0x80277871), (0x05E36E63), (0x8022B112), (0x057EFFDC), (0x801E3892), (0x051A8DF1), (0x801A0EF5), (0x04B618DF), (0x8016343D), (0x0451A1E6), (0x8012A875), (0x03ED2743), (0x800F6B8D), (0x0388AA33), (0x800C7D90),
//(0x03242AF6), (0x8009DE81), (0x02BFA9C9), (0x80078E61), (0x025B26E9), (0x80058D31), (0x01F6A296), (0x8003DAF2), (0x01921D0C), (0x800277A7), (0x012D968B), (0x8001634F), (0x00C90F4F), (0x80009DEB), (0x00648797), (0x8000277C), (0xFFFFFFA3), (0x80000002),
//(0xFF9B77AD), (0x8000277C), (0xFF36F0F5), (0x80009DEB), (0xFED269BA), (0x8001634E), (0xFE6DE338), (0x800277A6), (0xFE095DAF), (0x8003DAF1), (0xFDA4D95B), (0x80058D2F), (0xFD40567C), (0x80078E5F), (0xFCDBD54E), (0x8009DE7F), (0xFC775611), (0x800C7D8E),
//(0xFC12D902), (0x800F6B8A), (0xFBAE5E5E), (0x8012A872), (0xFB49E665), (0x80163444), (0xFAE57154), (0x801A0EFD), (0xFA80FF69), (0x801E389A), (0xFA1C90E1), (0x8022B11B), (0xF9B826FB), (0x8027786E), (0xF953BFF6), (0x802C8EA9), (0xF8EF5D0E), (0x8031F3BF),
//(0xF88AFE81), (0x8037A7AA), (0xF826A48E), (0x803DAA69), (0xF7C24F73), (0x8043FBF6), (0xF75DFF6D), (0x804A9C4E), (0xF6F9B4BB), (0x80518B6E), (0xF6956F99), (0x8058C950), (0xF6313046), (0x806055F0), (0xF5CCF701), (0x8068314A), (0xF568C405), (0x80705B59),
//(0xF5049793), (0x8078D418), (0xF4A071E6), (0x80819B81), (0xF43C543D), (0x808AB177), (0xF3D83CD6), (0x80941624), (0xF3742CEE), (0x809DC96B), (0xF31024C4), (0x80A7CB45), (0xF2AC2495), (0x80B21BAD), (0xF2482C9F), (0x80BCBA9C), (0xF1E43D1E), (0x80C7A80C),
//(0xF1805652), (0x80D2E3F5), (0xF11C7877), (0x80DE6E52), (0xF0B8A3CC), (0x80EA471A), (0xF054D88D), (0x80F66E47), (0xEFF116F8), (0x8102E3D1), (0xEF8D5F4B), (0x810FA7B0), (0xEF29B2C2), (0x811CB9BB), (0xEEC60F9D), (0x812A1A2D), (0xEE627718), (0x8137C8DB),
//(0xEDFEE972), (0x8145C5BE), (0xED9B66E6), (0x815410CE), (0xED37EFB3), (0x8162AA00), (0xECD48416), (0x8171914D), (0xEC71244C), (0x8180C6AB), (0xEC0DD092), (0x81904A11), (0xEBAA8927), (0x81A01B75), (0xEB474E46), (0x81B03ACD), (0xEAE4202D), (0x81C0A810),
//(0xEA80FF1A), (0x81D16333), (0xEA1DEB4A), (0x81E26C2C), (0xE9BAE5F5), (0x81F3C2C3), (0xE957ED61), (0x82056748), (0xE8F502C6), (0x82175982), (0xE8922662), (0x82299967), (0xE82F5872), (0x823C26EC), (0xE7CC9933), (0x824F0204), (0xE769E8E2), (0x82622AA6),
//(0xE70747BB), (0x8275A0C3), (0xE6A4B5FB), (0x82896452), (0xE64233E0), (0x829D7545), (0xE5DFC1A5), (0x82B1D390), (0xE57D5F89), (0x82C67F27), (0xE51B0DC6), (0x82DB77FC), (0xE4B8CC9B), (0x82F0BE03), (0xE4569D3D), (0x830650F8), (0xE3F47DF5), (0x831C313B),
//(0xE3926FFA), (0x83325E87), (0xE3307388), (0x8348D8D0), (0xE2CE88DB), (0x835FA007), (0xE26CB031), (0x8376B41E), (0xE20AE9C5), (0x838E1508), (0xE1A935D4), (0x83A5C2B5), (0xE147949A), (0x83BDBD17), (0xE0E60653), (0x83D60420), (0xE0848B3B), (0x83EE97C0),
//(0xE023238F), (0x840777E8), (0xDFC1CF8A), (0x8420A488), (0xDF608F69), (0x843A1D92), (0xDEFF645F), (0x8453E2B4), (0xDE9E4CB9), (0x846DF460), (0xDE3D49AB), (0x84885246), (0xDDDC5B6F), (0x84A2FC55), (0xDD7B8243), (0x84BDF27E), (0xDD1ABE62), (0x84D934AE),
//(0xDCBA1007), (0x84F4C2D6), (0xDC59776E), (0x85109CE4), (0xDBF8F4D3), (0x852CC2C7), (0xDB988872), (0x8549346E), (0xDB383285), (0x8565F1C8), (0xDAD7F348), (0x8582FAC2), (0xDA77CAF6), (0x85A04F4B), (0xDA17BAC1), (0x85BDEF05), (0xD9B7C0F8), (0x85DBDA73),
//(0xD957DECD), (0x85FA113A), (0xD8F8147A), (0x86189346), (0xD898623B), (0x86376085), (0xD838C84A), (0x865678E3), (0xD7D946E3), (0x8675DC4D), (0xD779DE41), (0x86958AB0), (0xD71A8E9D), (0x86B583F8), (0xD6BB5834), (0x86D5C812), (0xD65C3B40), (0x86F656E9),
//(0xD5FD37FB), (0x8717306B), (0xD59E4EA0), (0x87385481), (0xD53F7F6A), (0x8759C319), (0xD4E0CB84), (0x877B7BC6), (0xD4823147), (0x879D7F20), (0xD423B1DD), (0x87BFCCBD), (0xD3C54D82), (0x87E26488), (0xD367046F), (0x8805466A), (0xD308D6DE), (0x8828724F),
//(0xD2AAC50A), (0x884BE820), (0xD24CCF2D), (0x886FA7C8), (0xD1EEF581), (0x8893B132), (0xD191383F), (0x88B80446), (0xD13397A2), (0x88DCA0EE), (0xD0D613E3), (0x89018713), (0xD078AD3C), (0x8926B6A0), (0xD01B63E6), (0x894C2F7D), (0xCFBE3908), (0x8971F132),
//(0xCF612B01), (0x8997FC68), (0xCF043AF8), (0x89BE50A8), (0xCEA76927), (0x89E4EDDB), (0xCE4AB5C6), (0x8A0BD3E8), (0xCDEE210E), (0x8A3302B8), (0xCD91AB39), (0x8A5A7A32), (0xCD355480), (0x8A823A3F), (0xCCD91D1C), (0x8AAA42C5), (0xCC7D0545), (0x8AD293AC),
//(0xCC210D35), (0x8AFB2CDB), (0xCBC53524), (0x8B240E38), (0xCB697D4B), (0x8B4D37AC), (0xCB0DE6CB), (0x8B76A8B2), (0xCAB2700B), (0x8BA06204), (0xCA571A2C), (0x8BCA631F), (0xC9FBE567), (0x8BF4ABE9), (0xC9A0D1F3), (0x8C1F3C49), (0xC945E00A), (0x8C4A1423),
//(0xC8EB0FE3), (0x8C75335D), (0xC89061B6), (0x8CA099DE), (0xC835D5BB), (0x8CCC4789), (0xC7DB6C2A), (0x8CF83C44), (0xC781253B), (0x8D2477F5), (0xC7270126), (0x8D50FA7F), (0xC6CD0021), (0x8D7DC3C7), (0xC6732265), (0x8DAAD3B2), (0xC619682A), (0x8DD82A23),
//(0xC5BFD1A5), (0x8E05C6FF), (0xC5665F0F), (0x8E33AA2A), (0xC50D109F), (0x8E61D388), (0xC4B3E68C), (0x8E9042FB), (0xC45AE10D), (0x8EBEF868), (0xC4020058), (0x8EEDF3B1), (0xC3A94669), (0x8F1D33C8), (0xC350AFEE), (0x8F4CBA71), (0xC2F83EE1), (0x8F7C86A0),
//(0xC29FF37B), (0x8FAC9836), (0xC247CDF0), (0x8FDCEF16), (0xC1EFCE78), (0x900D8B21), (0xC197F548), (0x903E6C3B), (0xC1404298), (0x906F9245), (0xC0E8B69C), (0x90A0FD21), (0xC091518B), (0x90D2ACAF), (0xC03A139B), (0x9104A0D2), (0xBFE2FD02), (0x9136D96B),
//(0xBF8C0DF6), (0x9169565A), (0xBF3546AB), (0x919C1781), (0xBEDEA758), (0x91CF1CC0), (0xBE883032), (0x920265F8), (0xBE31E16E), (0x9235F309), (0xBDDBBB42), (0x9269C3D3), (0xBD85BDE2), (0x929DD837), (0xBD2FE985), (0x92D23013), (0xBCDA3E5E), (0x9306CB49),
//(0xBC84BCA2), (0x933BA9B7), (0xBC2F6487), (0x9370CB3D), (0xBBDA3641), (0x93A62FBB), (0xBB853205), (0x93DBD70E), (0xBB305806), (0x9411C116), (0xBADBA879), (0x9447EDB3), (0xBA872542), (0x947E5BAB), (0xBA32CB35), (0x94B50D09), (0xB9DE9C38), (0x94EC0097),
//(0xB98A987D), (0x95233631), (0xB936C039), (0x955AADB6), (0xB8E3139E), (0x95926705), (0xB88F92E2), (0x95CA61FA), (0xB83C3E37), (0x96029E73), (0xB7E915D0), (0x963B1C4D), (0xB79619E2), (0x9673DB66), (0xB7434A9E), (0x96ACDB9A), (0xB6F0A839), (0x96E61CC6),
//(0xB69E32E5), (0x971F9EC8), (0xB64BEAD5), (0x9759617B), (0xB5F9D03C), (0x979364BC), (0xB5A7E34D), (0x97CDA867), (0xB5562439), (0x98082C58), (0xB5049334), (0x9842F06B), (0xB4B33070), (0x987DF47C), (0xB461FC1F), (0x98B93866), (0xB410F672), (0x98F4BC06),
//(0xB3C01F9D), (0x99307F35), (0xB36F77D1), (0x996C81D0), (0xB31EFF3F), (0x99A8C3B0), (0xB2CEB61A), (0x99E544B2), (0xB27E9C92), (0x9A2204B0), (0xB22EB2DA), (0x9A5F0384), (0xB1DEF922), (0x9A9C4109), (0xB18F7130), (0x9AD9BBDF), (0xB140180C), (0x9B177652),
//(0xB0F0EF7B), (0x9B556F04), (0xB0A1F7AF), (0x9B93A5CF), (0xB05330D8), (0x9BD21A8C), (0xB0049B27), (0x9C10CD15), (0xAFB636CD), (0x9C4FBD43), (0xAF6803F9), (0x9C8EEAEF), (0xAF1A02DC), (0x9CCE55F3), (0xAECC33A5), (0x9D0DFE27), (0xAE7E9686), (0x9D4DE363),
//(0xAE312BAE), (0x9D8E0581), (0xADE3F34D), (0x9DCE6459), (0xAD96ED91), (0x9E0EFFC3), (0xAD4A1AAC), (0x9E4FD798), (0xACFD7ACC), (0x9E90EBAF), (0xACB10E20), (0x9ED23BE0), (0xAC64D4D8), (0x9F13C803), (0xAC18CF22), (0x9F558FF0), (0xABCCFD2E), (0x9F97937D),
//(0xAB815F2A), (0x9FD9D283), (0xAB35F545), (0xA01C4CD8), (0xAAEABFAE), (0xA05F0253), (0xAA9FBE92), (0xA0A1F2CC), (0xAA54F221), (0xA0E51E18), (0xAA0A5A88), (0xA128840F), (0xA9BFF7F5), (0xA16C2487), (0xA975CC0F), (0xA1AFFDFC), (0xA92BD411), (0xA1F412F7),
//(0xA8E211A2), (0xA23861F5), (0xA89884F1), (0xA27CEACD), (0xA84F2E2A), (0xA2C1AD53), (0xA8060D7A), (0xA306A95E), (0xA7BD2310), (0xA34BDEC3), (0xA7746F17), (0xA3914D57), (0xA72BF1BC), (0xA3D6F4F0), (0xA6E3AB2D), (0xA41CD562), (0xA69B9B96), (0xA462EE82),
//(0xA653C323), (0xA4A94026), (0xA60C2200), (0xA4EFCA21), (0xA5C4B85B), (0xA5368C48), (0xA57D865E), (0xA57D8670), (0xA5368C36), (0xA5C4B86D), (0xA4EFCA0F), (0xA60C2213), (0xA4A94014), (0xA653C335), (0xA462EE70), (0xA69B9BA8), (0xA41CD550), (0xA6E3AB3F),
//(0xA3D6F4DE), (0xA72BF1CF), (0xA3914D46), (0xA7746F29), (0xA34BDEB2), (0xA7BD2322), (0xA306A94D), (0xA8060D8D), (0xA2C1AD42), (0xA84F2E3D), (0xA27CEABB), (0xA8988504), (0xA23861E4), (0xA8E211B5), (0xA1F412E6), (0xA92BD424), (0xA1AFFF45), (0xA975CAA9),
//(0xA16C2476), (0xA9BFF808), (0xA12883FE), (0xAA0A5A9B), (0xA0E51E07), (0xAA54F234), (0xA0A1F2BB), (0xAA9FBEA5), (0xA05F0242), (0xAAEABFC1), (0xA01C4CC7), (0xAB35F559), (0x9FD9D272), (0xAB815F3E), (0x9F97936C), (0xABCCFD41), (0x9F558FDF), (0xAC18CF35),
//(0x9F13C7F2), (0xAC64D4EB), (0x9ED23BCF), (0xACB10E33), (0x9E90EB9E), (0xACFD7ADF), (0x9E4FD787), (0xAD4A1AC0), (0x9E0EFFB3), (0xAD96EDA5), (0x9DCE6449), (0xADE3F360), (0x9D8E0571), (0xAE312BC2), (0x9D4DE353), (0xAE7E969A), (0x9D0DFE16), (0xAECC33B9),
//(0x9CCE55E3), (0xAF1A02EF), (0x9C8EEADF), (0xAF68040D), (0x9C4FBD33), (0xAFB636E1), (0x9C10CD05), (0xB0049B3B), (0x9BD21A7C), (0xB05330EC), (0x9B93A5BF), (0xB0A1F7C3), (0x9B556EF4), (0xB0F0EF90), (0x9B177642), (0xB1401820), (0x9AD9BD09), (0xB18F6FB0),
//(0x9A9C40F9), (0xB1DEF936), (0x9A5F0375), (0xB22EB2EE), (0x9A2204A1), (0xB27E9CA6), (0x99E544A3), (0xB2CEB62E), (0x99A8C3A1), (0xB31EFF54), (0x996C81C0), (0xB36F77E5), (0x99307F26), (0xB3C01FB2), (0x98F4BBF6), (0xB410F687), (0x98B93857), (0xB461FC33),
//(0x987DF46D), (0xB4B33085), (0x9842F05C), (0xB5049349), (0x98082C49), (0xB556244E), (0x97CDA858), (0xB5A7E362), (0x979364AD), (0xB5F9D051), (0x9759616C), (0xB64BEAEA), (0x971F9EB9), (0xB69E32FA), (0x96E61CB8), (0xB6F0A84E), (0x96ACDB8B), (0xB7434AB3),
//(0x9673DB57), (0xB79619F7), (0x963B1C3F), (0xB7E915E5), (0x96029E64), (0xB83C3E4C), (0x95CA61EB), (0xB88F92F7), (0x959266F7), (0xB8E313B4), (0x955AADA8), (0xB936C04E), (0x95233623), (0xB98A9893), (0x94EC0089), (0xB9DE9C4D), (0x94B50E13), (0xBA32C99E),
//(0x947E5CB3), (0xBA8723A9), (0x9447EDA5), (0xBADBA88F), (0x9411C109), (0xBB30581B), (0x93DBD700), (0xBB85321A), (0x93A62FAD), (0xBBDA3657), (0x9370CB30), (0xBC2F649D), (0x933BA9AA), (0xBC84BCB8), (0x9306CB3C), (0xBCDA3E74), (0x92D23006), (0xBD2FE99B),
//(0x929DD829), (0xBD85BDF8), (0x9269C3C6), (0xBDDBBB58), (0x9235F2FC), (0xBE31E184), (0x920265EB), (0xBE883048), (0x91CF1CB3), (0xBEDEA76E), (0x919C1774), (0xBF3546C1), (0x9169564D), (0xBF8C0E0C), (0x9136D95E), (0xBFE2FD18), (0x9104A0C5), (0xC03A13B2),
//(0x90D2ACA2), (0xC09151A2), (0x90A0FD14), (0xC0E8B6B2), (0x906F9239), (0xC14042AE), (0x903E6C2F), (0xC197F55F), (0x900D8B15), (0xC1EFCE8E), (0x8FDCEF09), (0xC247CE07), (0x8FAC9829), (0xC29FF391), (0x8F7C8694), (0xC2F83EF8), (0x8F4CBA65), (0xC350B004),
//(0x8F1D34AD), (0xC3A944BC), (0x8EEDF3A5), (0xC402006F), (0x8EBEF85C), (0xC45AE123), (0x8E9042EF), (0xC4B3E6A3), (0x8E61D37C), (0xC50D10B6), (0x8E33AA1F), (0xC5665F26), (0x8E05C6F4), (0xC5BFD1BC), (0x8DD82A17), (0xC6196840), (0x8DAAD3A6), (0xC673227C),
//(0x8D7DC3BB), (0xC6CD0038), (0x8D50FA73), (0xC727013D), (0x8D2477E9), (0xC7812552), (0x8CF83C39), (0xC7DB6C41), (0x8CCC477E), (0xC835D5D2), (0x8CA099D3), (0xC89061CD), (0x8C753352), (0xC8EB0FFA), (0x8C4A1418), (0xC945E021), (0x8C1F3C3E), (0xC9A0D20A),
//(0x8BF4ABDF), (0xC9FBE57E), (0x8BCA6314), (0xCA571A43), (0x8BA061F9), (0xCAB27022), (0x8B76A8A7), (0xCB0DE6E2), (0x8B4D3738), (0xCB697E4C), (0x8B240DC5), (0xCBC53625), (0x8AFB2C68), (0xCC210E37), (0x8AD2933A), (0xCC7D0647), (0x8AAA4254), (0xCCD91E1E),
//(0x8A823A9A), (0xCD3553AD), (0x8A5A7A8D), (0xCD91AA66), (0x8A330312), (0xCDEE203A), (0x8A0BD442), (0xCE4AB4F1), (0x89E4EE34), (0xCEA76852), (0x89BE5100), (0xCF043A24), (0x8997FCBF), (0xCF612A2C), (0x8971F188), (0xCFBE3833), (0x894C2F73), (0xD01B63FE),
//(0x8926B697), (0xD078AD53), (0x8901870A), (0xD0D613FB), (0x88DCA0E4), (0xD13397BA), (0x88B8043C), (0xD1913857), (0x8893B128), (0xD1EEF599), (0x886FA7BF), (0xD24CCF45), (0x884BE817), (0xD2AAC522), (0x88287246), (0xD308D6F6), (0x88054661), (0xD3670487),
//(0x87E2647F), (0xD3C54D9A), (0x87BFCCB5), (0xD423B1F5), (0x879D7F18), (0xD482315F), (0x877B7BBD), (0xD4E0CB9C), (0x8759C2BA), (0xD53F8074), (0x87385424), (0xD59E4FAA), (0x8717300E), (0xD5FD3905), (0x86F6568E), (0xD65C3C4A), (0x86D5C7B7), (0xD6BB593F),
//(0x86B5839E), (0xD71A8FA8), (0x86958AF9), (0xD779DD66), (0x8675DC95), (0xD7D94608), (0x8656792A), (0xD838C76F), (0x863760CC), (0xD8986160), (0x8618938D), (0xD8F8139F), (0x85FA1180), (0xD957DDF1), (0x85DBDAB8), (0xD9B7C01C), (0x85BDEF49), (0xDA17B9E5),
//(0x85A04F43), (0xDA77CB0F), (0x8582FABB), (0xDAD7F360), (0x8565F1C1), (0xDB38329D), (0x85493467), (0xDB98888A), (0x852CC2C0), (0xDBF8F4EC), (0x85109CDD), (0xDC597787), (0x84F4C2CF), (0xDCBA1020), (0x84D934A7), (0xDD1ABE7A), (0x84BDF277), (0xDD7B825C),
//(0x84A2FC4F), (0xDDDC5B88), (0x8488523F), (0xDE3D49C3), (0x846DF45A), (0xDE9E4CD2), (0x8453E2AD), (0xDEFF6478), (0x843A1D4B), (0xDF60907A), (0x8420A441), (0xDFC1D09B), (0x840777A2), (0xE02324A0), (0x83EE977A), (0xE0848C4C), (0x83D603DC), (0xE0E60764),
//(0x83BDBCD4), (0xE14795AB), (0x83A5C2EC), (0xE1A934F4), (0x838E153E), (0xE20AE8E5), (0x8376B454), (0xE26CAF51), (0x835FA03C), (0xE2CE87FB), (0x8348D904), (0xE33072A7), (0x83325EBA), (0xE3926F19), (0x831C316D), (0xE3F47D14), (0x8306512A), (0xE4569C5C),
//(0x82F0BDFE), (0xE4B8CCB4), (0x82DB77F7), (0xE51B0DDF), (0x82C67F21), (0xE57D5FA2), (0x82B1D38B), (0xE5DFC1BE), (0x829D7540), (0xE64233F9), (0x8289644D), (0xE6A4B614), (0x8275A0BE), (0xE70747D4), (0x82622AA1), (0xE769E8FB), (0x824F0200), (0xE7CC994C),
//(0x823C26E7), (0xE82F588C), (0x82299962), (0xE892267C), (0x8217597D), (0xE8F502DF), (0x82056743), (0xE957ED7A), (0x81F3C2BF), (0xE9BAE60E), (0x81E26BFB), (0xEA1DEC5F), (0x81D16303), (0xEA810030), (0x81C0A7E1), (0xEAE42143), (0x81B03AA0), (0xEB474F5C),
//(0x81A01B48), (0xEBAA8A3D), (0x81904A35), (0xEC0DCFAF), (0x8180C6CF), (0xEC712368), (0x81719170), (0xECD48332), (0x8162AA22), (0xED37EECF), (0x815410EF), (0xED9B6602), (0x8145C5DF), (0xEDFEE88E), (0x8137C8FB), (0xEE627634), (0x812A1A4C), (0xEEC60EB9),
//(0x811CB9DA), (0xEF29B1DD), (0x810FA7AD), (0xEF8D5F65), (0x8102E3CE), (0xEFF11711), (0x80F66E44), (0xF054D8A6), (0x80EA4717), (0xF0B8A3E5), (0x80DE6E4F), (0xF11C7891), (0x80D2E3F2), (0xF180566B), (0x80C7A809), (0xF1E43D38), (0x80BCBA99), (0xF2482CB8),
//(0x80B21BAA), (0xF2AC24AF), (0x80A7CB42), (0xF31024DE), (0x809DC968), (0xF3742D08), (0x80941622), (0xF3D83CEF), (0x808AB175), (0xF43C5456), (0x80819B68), (0xF4A072FF), (0x8078D3FF), (0xF50498AB), (0x80705B42), (0xF568C51E), (0x80683134), (0xF5CCF819),
//(0x806055DB), (0xF631315F), (0x8058C93B), (0xF69570B2), (0x80518B7E), (0xF6F9B3D5), (0x804A9C5E), (0xF75DFE87), (0x8043FC05), (0xF7C24E8D), (0x803DAA77), (0xF826A3A8), (0x8037A7B8), (0xF88AFD9B), (0x8031F3CB), (0xF8EF5C28), (0x802C8EB5), (0xF953BF10),
//(0x80277879), (0xF9B82615), (0x8022B119), (0xFA1C90FB), (0x801E3899), (0xFA80FF82), (0x801A0EFC), (0xFAE5716D), (0x80163443), (0xFB49E67F), (0x8012A872), (0xFBAE5E78), (0x800F6B8A), (0xFC12D91B), (0x800C7D8D), (0xFC77562A), (0x8009DE7E), (0xFCDBD568),
//(0x80078E5F), (0xFD405695), (0x80058D2F), (0xFDA4D975), (0x8003DAF1), (0xFE095DC8), (0x800277A6), (0xFE6DE352), (0x8001634E), (0xFED269D3), (0x80009DEA), (0xFF36F10F), (0x8000277B), (0xFF9B78C7), (0x00000009), (0x9FC5AC28), (0x00000001), (0x00000200),
//(0x00000400), (0x8000FFD0), (0x0000000A), (0x00000009), (0x9FC5E1D8), (0x9FC56DCC), (0x800022D8), (0x9FC2CCEC), (0x8000FFD0), (0x00000000), (0x000000A0), (0x8000FF68), (0x00000000), (0xFFFFFFFF), (0x8FFFFC60), (0x00000400), (0x80012B08), (0x8000FFD0),
//(0x8000F3A0), (0x80012990), (0x9FC57FCC), (0x9FC181E8), (0x00000004), (0x8000F3C4), (0x800127D9), (0x80010FD0), (0x00000009), (0xFFFFFFFF), (0x800120E8), (0x9FC0E9E0), (0x8000F3C8), (0x00000002), (0x80012378), (0x8000F3C4), (0x8000F3A0), (0xFFFFFFFF),
//(0x8000C774), (0x00000000), (0x00000000), (0x9FC134A4), (0x00000002), (0x9FC5AC28), (0x8000F3A0), (0x00000002), (0x800022D8), (0x00000001), (0x8000F3A0), (0x00000000), (0x80012AF8), (0x00000002), (0x00000001), (0x9FC0D3BC), (0x8000C774), (0x9FC07600),
//(0x00000003), (0x8FFFFCB0), (0x00000002), (0x8000C9A0), (0x8FFFFC00), (0x00000000), (0x01010000), (0x00000000), (0x8000F420), (0x00000000), (0x00000001), (0x00000000), (0x0000FFC8), (0x00000007), (0x8000F3E8), (0x800042D8), (0x00000000), (0x8000C76C),
//(0x8000F3A0), (0x8000C774), (0x00000000), (0x00010000), (0x00000001), (0x800022D8), (0x80000548), (0x9FC06E18), (0x9FC06380), (0x00000000), (0x800022D8), (0x00000001), (0x8000F3A0), (0x8FFFFD60), (0x8000F3A0), (0x8000C760), (0x00000000), (0x80000648),
//(0x8000F3A0), (0x8FFFFF88), (0x00000000), (0x00002000), (0x800042D8), (0x800022D8), (0x9FC0386C), (0x9FC0385C), (0x00000005), (0x8FFFFF88), (0x9FC60B99), (0x80000548), (0x80002108), (0x80002100), (0x80002104), (0x8FFFFEE8), (0x80000648), (0x9FC3FD3C),
//(0x00000000), (0x80001698), (0x9FC47D40), (0x00000000), (0x00000004), (0x800018C0), (0x8FFFFE00), (0x800018C4), (0x9FC60A50), (0x01040200), (0x2E342E31), (0x00000032), (0x00000011), (0x8FFFFE00), (0x9FC52984), (0x9FC60A5C), (0x00000000), (0x8FFFFE00),
//(0x9FC52978), (0x01040400), (0x2E342E31), (0x9FC60034), (0x00000000), (0x00000000), (0x9FC52984), (0x9FC52990), (0x0001F31F), (0x9FC630C4), (0x9FC49524), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x9FC6310C), (0x00000000),
//(0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x9FC4A214), (0x800018C0), (0x00000001), (0x9FC47468), (0x00000000), (0x00000000), (0x00000001), (0x8FFFFEC8), (0x00000000), (0x80001978),
//(0x9FC47D40), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x9FC4783C), (0x00000000), (0x00000000), (0x80001978), (0x00000003), (0x9FC63030), (0x9FC60000), (0x00000000), (0x10000000), (0x80001614), (0x80001698), (0x9FC48B08), (0x9FC63010),
//(0x9FC6307C), (0x9FC63001), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x9FC6372C), (0x00000007), (0x8000BD68), (0x00000000), (0x00000005), (0x8FFFFF88), (0x00000000), (0x9FC4D5EC), (0x00000000), (0x10000000),
//(0x9FC49264), (0x9FC49258), (0x00000000), (0x00000002), (0x00000000), (0x00000000), (0x00000001), (0xFFFFFFFF), (0x00000005), (0x8FFFFF88), (0x00000000), (0x10000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x20000000), (0x9FC014BC),
//(0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000),
//(0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x8FFFFFA1), (0x8FFFFFDD), (0x8FFFFFE1), (0x8FFFFFF4), (0x8FFFFFF8), (0x00000000), (0x6F682F00), (0x6C2F656D), (0x6D2F686F), (0x532F746E), (0x732F4E56), (0x432F6564),
//(0x685F4B44), (0x63616165), (0x2F636564), (0x2F6E6962), (0x44636161), (0x646F6365), (0x735F7265), (0x4C5F6564), (0x78756E69), (0x66692D00), (0x65687400), (0x74736574), (0x5F313434), (0x612E636C), (0x00737464), (0x00666F2D), (0x2E706D74), (0x00766177),
//(0x8FB40060), (0x10000008), (0x8FB50064), (0x02403021), (0x0FF13E42), (0x02603821), (0x0040A021), (0x0060A821), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x15000007), (0x00000000),
//(0x106B0007), (0x00000000), (0x0BF14379), (0x00000000), (0x1080FFF7), (0x00000000), (0x02E03821), (0x02C02021), (0x1440FF96), (0x02E02821), (0x02802021), (0x1000FF93), (0x02A02821), (0x8FA60028), (0x8FA60028), (0x8FA7002C), (0x00402021), (0x00602821),
//(0x00409021), (0x00609821), (0xAFA20060), (0x0FF141EE), (0x8CC42D48), (0x8CC52D4C), (0x0FF144AE), (0x24060036), (0x8FA40068), (0x8FA5006C), (0x00403021), (0x00603821), (0x8FB20040), (0x8FB1003C), (0x8FB00038), (0x00801021), (0x00A01821), (0x03E00008),
//(0x27BD0060), (0x00002821), (0x00000000), (0x00000000), (0x00000000), (0x8000C668), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x52082450), (0x3EED1705), (0xF20FF8A1), (0x89F6E17F), (0x17D7DC00), (0x09B9F2C3), (0x6378968C), (0xAF0607F6),
//(0xFDFDE0C9), (0x3CE3DFEE), (0x5168229E), (0x7CF79734), (0xF5FFF56F), (0x30700093), (0x135F5C75), (0xE6D73EEE), (0x6E400DF9), (0x58252ACB), (0x557311CA), (0x5539B303), (0x56557355), (0xB6BDACEF), (0x59F9428D), (0x9AE63020), (0x558FF7E7), (0x7955806D),
//(0x09D549F3), (0x603BF5B7), (0x3134413B), (0xBCA7C1AA), (0xA98D1339), (0x57B29C97), (0x50F1FF07), (0xE4A13980), (0xD0881A21), (0xC1FFE30A), (0xB0A66C19), (0x18416490), (0x160E0214), (0xA584CBB5), (0x02AAA82A), (0x73981340), (0x964A24DE), (0x6E829FC4),
//(0xDFD62360), (0x4C3BF152), (0x38721574), (0xD46BC912), (0xBBD65F9D), (0xE6F02C9D), (0x867C6EEC), (0x43FAB725), (0xA4144419), (0x69BB6AAF), (0xCE27B744), (0x022241A9), (0x1EB7FDD8), (0x1CA6CBC0), (0xBBCBC111), (0xA6FD1403), (0x76C688D9), (0xB47186CB),
//(0x5E98936D), (0x9BBF7E2E), (0x8B7FBCEC), (0x9DAF7EF5), (0x7E3A79F1), (0x9C896ACD), (0x628F249A), (0xDD6E51AF), (0xAF73994B), (0x5966995D), (0xB7620597), (0x369B1E54), (0x7CEDC787), (0xE7B15B1F), (0xB36F5F7D), (0xB5B73EF6), (0xD76EEEE2), (0x14E86186),
//(0xC05DF14E), (0x8050F1FF), (0x218C2131), (0xBE25941A), (0x9041CD06), (0x1EE8A9C0), (0xD530FEDD), (0x7495C808), (0xCC159291), (0x4EC85CA3), (0xEC3C704E), (0x0B6633B3), (0x4D992D4B), (0xEB1B5774), (0xDFB9D508), (0x11783719), (0x00000000), (0x00000000),
//(0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x37000000), (0x000080A3), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x8C872F24), (0x0FF11B13), (0x02002021), (0x3C069FC6),
//(0x00602821), (0x0060A821), (0x24C32D48), (0x8C660000), (0x00803021), (0x0FF13AA0), (0x00A03821), (0x1000FFEC), (0x00402021), (0x8FA60068), (0x0FF13E42), (0x8FA7006C), (0x02002021), (0x00602821), (0x0FF141EE), (0x00608821), (0x04400016), (0x02002021),
//(0x3C059FC6), (0x8CA62E78), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x5C5D4A7F), (0x5C8016FF), (0x5CA2CA7F), (0x5CC575FF), (0x5CE80FFF), (0x5D0A997F), (0x5D2D1AFF), (0x5D4F8B7F),
//(0x8F87815C), (0x00402021), (0x0FF13BD0), (0x00602821), (0x00402021), (0x0FF13E45), (0x00602821), (0x0276202A), (0x004B500B), (0x24030001), (0x1543FFE9), (0x714B5820), (0x000F3D00), (0x00003021), (0x00C01021), (0x03E00008), (0x014B6825), (0x000860C0),
//(0x00007021), (0x00007821), (0x00005021), (0x10000013), (0x3C0B0040), (0x008C182B), (0x8FFF7570), (0xAE290094), (0xAE1100A8), (0x15070023), (0x8FFF7580), (0x8E0F0098), (0x8DE50020), (0x50A00020), (0xAFB20020), (0xAFB1001C), (0xAFBF0028), (0xAFB00018),
//(0x8C900020), (0x00A08821), (0x8E020008), (0x8CA50008), (0x0FF003A4), (0x00003021), (0x04400031), (0x2403001D), (0x8E260008), (0x8E25000C), (0xAE060008), (0xAE05000C), (0x0FF123AB), (0x00000000), (0x00409021), (0x8E2A0010), (0x2E490001), (0x000A402B),
//(0x1000FFEA), (0x01091024), (0x8E240008), (0x8E25000C), (0x8E660018), (0xAE05000C), (0xAE040008), (0x8E07000C), (0x8CC30094), (0x0067582A), (0x8FBF0028), (0xACC40090), (0xACC50094), (0x02401821), (0x8FBF0028), (0x8FB30024), (0x8FB20020), (0x8FB1001C),
//(0x27A40070), (0x12000005), (0x8FA20094), (0x0FF11CDB), (0x00000000), (0xAC500000), (0x2402FFFF), (0x8FBF00BC), (0x00000000), (0x10000009), (0x8E430000), (0x12200006), (0x00000000), (0x964E0010), (0x2403FFFF), (0x35CD0040), (0x00000000), (0x00000000),
//(0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0xFF7B0000), (0x2E8050F1), (0x10214482), (0x1BA00005), (0x0000C0FF), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000),
//(0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000),
//(0x00000000), (0x00000000), (0x2463FFF8), (0x1060000E), (0x270703FF), (0x00601027), (0x00692804), (0x00481006), (0x00682004), (0x00021042), (0xB56DA974), (0xCACDB07C), (0x4EE1ADD4), (0xEF859B34), (0x89F47B47), (0x515C155A), (0x38D06757), (0x8050F1FF),
//(0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x8FFF6F50), (0x54414C20), (0x7573204D), (0x726F7070), (0x8FFF6F60), (0x0000000A), (0x2D2D202A), (0x57415220), (0x00000000), (0x00000000),
//(0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000),
//(0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00602821), (0x02403021), (0x0FF13D01), (0x02603821), (0x1000FEEF), (0x00402021), (0x02002021), (0x02202821), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000),
//(0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0xA4370000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000),
//(0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0xFF770000), (0x2E8050F1), (0x1A214462), (0x04D6D583), (0x40031282),
//(0x6469A735), (0x0B4C99A3), (0x0AF88594), (0xF90969D7), (0x96131C92), (0x025EEA10), (0x3A1FE421), (0x614FF390), (0x1CFDC327), (0xC177E04F), (0xF4D87E82), (0x78253F39), (0xBB839F94), (0x998B3EB1), (0x80F28098), (0xDF3FABCD), (0x24D840AC), (0xBA95321C),
//(0xDC211D20), (0x15041942), (0x61858ABD), (0x546084A1), (0xA70D219C), (0x7BD52135), (0x78060F71), (0xEA3C87D8), (0x3FAF3A24), (0xE4B2C421), (0xB99D1453), (0x2741E264), (0x2239813A), (0x2944DA20), (0x441EF7C4), (0x0BD0B720), (0xED84EA26), (0x73E0C0D2),
//(0x678E3039), (0x21420109), (0x8607492E), (0x28CEF440), (0x022768C8), (0xF68E3611), (0x84E84D55), (0x73004C04), (0xF9B6630E), (0x677FFA8F), (0x530CB18F), (0x2C4EE310), (0xABC7537C), (0x82CFDBCB), (0x100C63A2), (0x876D75BA), (0xC683F888), (0x0CDC1E1E),
//(0xA600E833), (0x33036066), (0x4305049C), (0x40890713), (0x9D12532E), (0x7B6E2FE2), (0xE244CC09), (0x9FFB2082), (0xAB3735D3), (0x00000080), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000),
//(0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x8D062F90), (0x8D072F94), (0x00402021), (0x0FF13D01),
//(0x00602821), (0x00403021), (0x02002021), (0x02202821), (0x00000000), (0x00000000), (0xF1FF7A00), (0xE23A8050), (0x841A2114), (0xC588DA75), (0x02400143), (0xAED6AAD7), (0x94C2545B), (0x3E46156C), (0x05BFDF27), (0xB0822075), (0xD24C7BC4), (0x62122054),
//(0x8CC41C49), (0x2C414972), (0x80C5DA0E), (0xD84A007C), (0x719E73C0), (0x765014B0), (0x0A391BC8), (0x60A70223), (0x54CDA46B), (0x4606D455), (0x37B6A738), (0xFFBB7D71), (0x5FEDD12F), (0xEE47FDC8), (0x3DC7913F), (0xEAEEF7FD), (0x219DADEC), (0xF9E53F5F),
//(0xB3D82D9E), (0xBBB79BCB), (0x43AD2AAB), (0x0E7ADBC0), (0xABBD0952), (0x1A6EF43A), (0xCD9B2A9D), (0x7222B366), (0x979E89E9), (0x02C9113F), (0x1D511466), (0x57A92206), (0x4A412075), (0xFD31D783), (0xA7542EFC), (0x24B76975), (0xB99962C7), (0x19F1D356),
//(0x9D12C3CB), (0x6D37D542), (0x29E7214D), (0x8900F645), (0xB154AD4A), (0xF047D988), (0xF55EE1D5), (0xC00B6932), (0x85ACE912), (0x8208B1B9), (0x80E14170), (0xA1D67C3F), (0x058035B3), (0xA4FE36CA), (0xA58ABACC), (0xC3E9831C), (0x27531B8C), (0xADBB8698),
//(0x06801C12), (0xAFAEEC8E), (0xC3269291), (0x5C2E01A0), (0x4A61087C), (0x60F21E7B), (0x6B92C670), (0xC9872364), (0x48464C97), (0x342E6499), (0xA6849A48), (0xF034002D), (0x442B4488), (0x160B0442), (0xED52CF50), (0x4D436C05), (0x6841C602), (0xA14F8A82),
//(0x9AC20DDE), (0x5B9251EE), (0x8ED998D5), (0x92B6EA22), (0x5B136C4B), (0x97C0A45B), (0x459C94A4), (0xCD9583C0), (0x6C589DF8), (0x3C9A17CD), (0x906601BF), (0x84D612E1), (0x67176F5F), (0xE47D5A44), (0x37386D5E), (0x92A4D61B), (0x28C513FB), (0x6E91599A),
//(0x653DA16D), (0xA19C39C0), (0x427690AD), (0x7005461C), (0xAB6C1EE4), (0x80BACC2D), (0x780A18C5), (0x822DC774), (0xB6043036), (0x00789C18), (0x106C9507), (0x4103A864), (0xC511585B), (0xF3E9B7F8), (0x24D79C4D), (0x31B20EB5), (0xB1C0483E), (0xE25C56B9),
//(0x96991096), (0x7CD4121F), (0x8A3B83F4), (0x704271C2), (0xB5C26ACC), (0x2B131AF0), (0x8B7FBCEC), (0x9DAF7EF5), (0x7E3A79F1), (0x9C896ACD), (0x628F249A), (0xDD6E51AF), (0xAF73994B), (0x5966995D), (0xB7620597), (0x369B1E54), (0x7CEDC787), (0xE7B15B1F),
//(0xB36F5F7D), (0xB5B73EF6), (0xD76EEEE2), (0x14E86186), (0xCC159291), (0x4EC85CA3), (0xEC3C704E), (0x0B6633B3), (0x4D992D4B), (0xEB1B5774), (0xDFB9D508), (0x11783719), (0x77BB10B0), (0xFCDF8350), (0xD34A7C65), (0xEF6E57BB), (0xECD56C68), (0x8EB7CA36),
//(0x2F26B136), (0x55631269), (0xEF1213E1), (0xA56AB844), (0xAA4B1D64), (0x64341EA9), (0x0CE9DB48), (0x460C2144), (0xADC50205), (0x515C472F), (0x136AC219), (0x4716CED1), (0xC8B8CE4E), (0x1D168210), (0x92334A81), (0x90DC910C), (0x49A00220), (0xE5F2C831),
//(0xB67DC785), (0x3C495174), (0x0D1F4C2A), (0x3AD787FD), (0xE9C9FB27), (0x7DFC2713), (0xEB39C4B3), (0x96F75F18), (0xACC3B410), (0x0FF127C3), (0x00000000), (0x8E2AB3FC), (0x000A4827), (0x004A4021), (0x01092824), (0x00A28823), (0xAFBF0034), (0xAFB7002C),
//(0xAFB60028), (0xAFB50024), (0xAFB40020), (0xAFB3001C), (0xAFB20018), (0x3C1E9FC6), (0x006B502B), (0x51400002), (0xAE23BDA0), (0x24120016), (0x0FF007E8), (0x02802021), (0x12400005), (0x02001021), (0x02118021), (0x0FF127C3), (0x02002021), (0x2407FFFF),
//(0x00502021), (0x10470004), (0x00002821), (0x3C0B8001), (0x8C630004), (0xAC860000), (0x00C02021), (0x1000FFEB), (0x8CC60000), (0xAC800000), (0x0FF0FD25), (0x24040002), (0x1C0B8260), (0x00000000), (0x33C7F780), (0x33F5E840), (0x3423D940), (0x3451B940),
//(0x347F9940), (0x34AD6880), (0x8E080008), (0xAE080004), (0x960C0010), (0xAE040000), (0x358A0002), (0x314BFFFF), (0x31691000), (0x31640008), (0x960D0010), (0x31A4FFF7), (0xA6040010), (0x960F0010), (0x31EEEFFF), (0xA60E0010), (0x8FBF0014), (0x8FB00010),
//(0x964E0010), (0x8E4A0000), (0x1540001F), (0x00C08821), (0x8E530008), (0xAE530004), (0xAE460000), (0x86440012), (0x10000013), (0xA64D0010), (0x8E430000), (0x8E510004), (0x2462FFFF), (0xAE420000), (0xA2340000), (0x965F0010), (0x529DA0D8), (0xBC180017),
//(0x19AB803B), (0x83F00020), (0x50F1FF07), (0xD4C02D80), (0xFC881A21), (0xC1BF0100), (0x5841680B), (0x103B2CC0), (0xB042C28A), (0x2324E8A8), (0x31531430), (0xEF71DD0E), (0x7C1B9D8E), (0x0998C228), (0x8FFF76F0), (0x00000000), (0x00000000), (0x00000000),
//(0x8FFF7700), (0x00000000), (0x00000000), (0x00000000), (0x03E00008), (0x2402FFFF), (0x1580FFFD), (0x00000000), (0x0BF14379), (0x00000000), (0x3C0F7FFF), (0x35EFFFFF), (0x12000024), (0x8FAA049C), (0x27A40020), (0x8FB50498), (0x02449823), (0x02B32821),
//(0x31110008), (0x1220000C), (0xB56DA974), (0xCACDB07C), (0x4EE1ADD4), (0xEF859B34), (0x89F47B47), (0x515C155A), (0x38D06757), (0x8050F1FF), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000),
//(0x8FFF76B0), (0x00601021), (0x03E00008), (0x27BD0028), (0x8FFF76C0), (0x00A04021), (0x00C03821), (0x1460003A), (0x8FB40020), (0x8FB3001C), (0x8FB20018), (0x8FB10014), (0x8FB00010), (0x03E00008), (0x27BD0028), (0x27BDFFE0), (0x02402021), (0x02602821),
//(0x0FF01178), (0x2631FFFF), (0x1620FFF9), (0x26100008), (0x26940001), (0x2A83000C), (0x80000948), (0x3FFFFFFF), (0x7FFFFFFD), (0x80000748), (0x9FC5DFC8), (0x764230FC), (0x30FC7642), (0x00000000), (0x8FB304EC), (0x26B20003), (0x26710001), (0x2414FFFC),
//(0x02548024), (0xAFB104EC), (0x92320000), (0x8E060000), (0x70C3F802), (0x24590001), (0xAFB904EC), (0x03F2C021), (0x93320000), (0x265EFFD0), (0x2FD7000A), (0x16E0FFF7), (0x9FC409DC), (0x9FC41AE4), (0x9FC41AE4), (0x9FC41AE4), (0x9FC41AE4), (0x9FC41AE4),
//(0x9FC41AE4), (0x9FC41AE4), (0xA600E833), (0x33036066), (0x4305049C), (0x40890713), (0x9D12532E), (0x7B6E2FE2), (0xE244CC09), (0x9FFB2082), (0x1460FFE9), (0x02A02021), (0x0FF0121E), (0x02402021), (0x0FF0118C), (0x00002021), (0x8FA54018), (0x0FF1445E),
//(0x00004812), (0x00008810), (0x000D6AC0), (0x00830019), (0x01A88025), (0x00106002), (0x00003810), (0x00001012), (0x71830001), (0x01625021), (0x014E2821), (0x00008812), (0x014B402B), (0x00AA302B), (0x000F7002), (0x00005010), (0xBA3670F0), (0x699195A2),
//(0x172D2FD0), (0xBE386D51), (0xC4AEA51C), (0x2AA77D7D), (0x8B51D3B4), (0xCF490FD9), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x8880B388), (0x4D596B2D), (0x37D74DCF), (0x58138B38),
//(0x5B7BCD4E), (0xAE6C4422), (0x4E5D1AF1), (0xDE7396A5), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x0100FEF7), (0x000000C8), (0x8000C0C0), (0x00000000), (0x00000000), (0x00000000),
//(0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000),
//(0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000001), (0x000021B2), (0x00000000), (0x00000000), (0x00000000), (0x9FC6307C), (0x00000000),
//(0x00060001), (0x00000001), (0x00000000), (0x8000C0D0), (0x8000C008), (0x00000000), (0x736E6F63), (0x00656C6F), (0x0100FEF7), (0x000000C8), (0x8000C188), (0x00000000), (0x00000000), (0x00000000), (0x00402021), (0x00602821), (0x02C03021), (0x0FF11ABF),
//(0x02E03821), (0x00408021), (0x3C029FC6), (0x8C462E70), (0x00000000), (0x00000000), (0x00000000), (0x8000C668), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x52082450), (0x3EED1705), (0xF20FF8A1), (0x89F6E17F), (0x17D7DC00), (0x09B9F2C3),
//(0x6378968C), (0xAF0607F6), (0xFDFDE0C9), (0x3CE3DFEE), (0x5168229E), (0x7CF79734), (0xF5FFF56F), (0x30700093), (0x135F5C75), (0xE6D73EEE), (0x6E400DF9), (0x58252ACB), (0x557311CA), (0x5539B303), (0x56557355), (0xB6BDACEF), (0x59F9428D), (0x9AE63020),
//(0x8000C0D0), (0x00000000), (0x00797474), (0x24030001), (0x0100FEF7), (0x000000C8), (0x8000C250), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000),
//(0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000),
//(0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x000021B2), (0x00000000), (0x00000000), (0x00000000), (0x9FC6307C), (0x00000000), (0x00080001), (0x00000001), (0x00000000), (0x00000000), (0x8000C198), (0x00000000), (0x30797474), (0x10470000),
//(0x0100FEF7), (0x00000408), (0x2A2A2A2A), (0x2A2A2A2A), (0x2A2A2A2A), (0x2A2A2A2A), (0x2A2A2A2A), (0x2A2A2A2A), (0x2A2A2A2A), (0x2A2A2A2A), (0x77BB10B0), (0xFCDF8350), (0xD34A7C65), (0xEF6E57BB), (0xECD56C68), (0x8EB7CA36), (0x2F26B136), (0x55631269),
//(0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x37000000), (0x000080A3), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x8C872F24), (0x0FF11B13),
//(0x02002021), (0x3C069FC6), (0x00602821), (0x0060A821), (0x24C32D48), (0x8C660000), (0x00803021), (0x0FF13AA0), (0x00A03821), (0x1000FFEC), (0x00402021), (0x8FA60068), (0x0FF13E42), (0x8FA7006C), (0x00000000), (0x00000000), (0x00000000), (0x00000000),
//(0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000001), (0x00000001), (0x00000000), (0x00000000), (0x00000000), (0x00000000),
//(0x00000001), (0x10800004), (0x0100FEF7), (0x00000168), (0x80012538), (0x80012548), (0x80012648), (0x80012748), (0x80010FD0), (0x80010FD0), (0x800111D0), (0x800113D0), (0x800115D0), (0x800117D0), (0x800119D0), (0x80011BD0), (0x80011DD0), (0x800127C8),
//(0x800127D9), (0x8001297A), (0x80012985), (0x8000FF78), (0x8000FF08), (0x80011FE0), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x37000000), (0x000080A3), (0x00000000), (0x00000000), (0x00000000), (0x00000000),
//(0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x8E040000), (0x8CE50000), (0x8CE60004), (0x0FF003A2), (0x24120005), (0x00402021), (0x10400004), (0x02202821), (0x0FF123AB), (0x00000000), (0x00409021), (0x8E2A0010),
//(0x2E490001), (0x000A402B), (0x1000FFEA), (0x01091024), (0x8E240008), (0x8E25000C), (0x8E660018), (0xAE05000C), (0xAE040008), (0x8E07000C), (0x8CC30094), (0x0067582A), (0x8FBF0028), (0xACC40090), (0xACC50094), (0x02401821), (0x8FBF0028), (0x8FB30024),
//(0x8FB20020), (0x8FB1001C), (0x27A40070), (0x12000005), (0x8FA20094), (0x0FF11CDB), (0x00000000), (0xAC500000), (0x2402FFFF), (0x8FBF00BC), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000),
//(0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000),
//(0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0xA4370000), (0x00000000), (0x00000000),
//(0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000),
//(0x00000000), (0xFF770000), (0x02203821), (0x0FF13BD0), (0x00602821), (0x3C049FC6), (0x8C862F40), (0x8C872F44), (0x00602821), (0x0FF13D01), (0x8FFF6F50), (0x54414C20), (0x7573204D), (0x726F7070), (0x8FFF6F60), (0x0000000A), (0x2D2D202A), (0x57415220),
//(0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000),
//(0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x678E3039), (0x21420109), (0x8607492E), (0x28CEF440), (0x022768C8), (0xF68E3611), (0x84E84D55), (0x73004C04), (0xF9B6630E), (0x677FFA8F), (0x530CB18F), (0x2C4EE310),
//(0xABC7537C), (0x82CFDBCB), (0x100C63A2), (0x876D75BA), (0xC683F888), (0x0CDC1E1E), (0xA600E833), (0x33036066), (0x4305049C), (0x40890713), (0x9D12532E), (0x7B6E2FE2), (0xE244CC09), (0x9FFB2082), (0xAB3735D3), (0x00000080), (0x00000000), (0x00000000),
//(0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000),
//(0x00000000), (0x00000000), (0x00000000), (0x00000000), (0xF1FF7A00), (0xE23A8050), (0x841A2114), (0xC588DA75), (0x02400143), (0xAED6AAD7), (0x94C2545B), (0x3E46156C), (0x05BFDF27), (0xB0822075), (0xD24C7BC4), (0x62122054), (0xCEDC07F4), (0xEAB2DE00),
//(0x045BC1E8), (0x82B2AE86), (0xE4BF2823), (0x9A01B47A), (0x1769438E), (0x015C7986), (0xA70D219C), (0x7BD52135), (0x78060F71), (0xEA3C87D8), (0x3FAF3A24), (0xE4B2C421), (0xB99D1453), (0x2741E264), (0x2239813A), (0x2944DA20), (0x441EF7C4), (0x0BD0B720),
//(0xED84EA26), (0x73E0C0D2), (0x678E3039), (0x21420109), (0x8607492E), (0x28CEF440), (0x022768C8), (0xF68E3611), (0x84E84D55), (0x73004C04), (0xF9B6630E), (0x677FFA8F), (0x530CB18F), (0x2C4EE310), (0xABC7537C), (0x82CFDBCB), (0x100C63A2), (0x876D75BA),
//(0xC683F888), (0x0CDC1E1E), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000),
//(0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000),
//(0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000),
//(0x00000000), (0x00000000), (0x00000000), (0x00000000), (0xA606FB2A), (0xE4CE1557), (0x73240820), (0x8037A8B4), (0xA1D88032), (0x75A3F1C7), (0x32BB0370), (0x5F850242), (0x37B6A738), (0xFFBB7D71), (0x5FEDD12F), (0xEE47FDC8), (0x3DC7913F), (0xEAEEF7FD),
//(0x219DADEC), (0xF9E53F5F), (0xB3D82D9E), (0xBBB79BCB), (0x43AD2AAB), (0x0E7ADBC0), (0xABBD0952), (0x1A6EF43A), (0xCD9B2A9D), (0x7222B366), (0x979E89E9), (0x02C9113F), (0x1D511466), (0x57A92206), (0x4A412075), (0xFD31D783), (0xA7542EFC), (0x24B76975),
//(0xB99962C7), (0x19F1D356), (0x9D12C3CB), (0x6D37D542), (0x29E7214D), (0x8900F645), (0xB154AD4A), (0xF047D988), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000),
//(0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x00010100), (0x00000000), (0x8000C9E8), (0x8000CBF0), (0x8000CCD0), (0x8000CDB0), (0x8000CE90), (0x00000000), (0x2F26B136), (0x55631269), (0xEF1213E1), (0xA56AB844),
//(0xAA4B1D64), (0x64341EA9), (0x0CE9DB48), (0x460C2144), (0xADC50205), (0x515C472F), (0x304C29BD), (0x76AF8711), (0x557CADB7), (0x2A9BBC8E), (0xE50E145B), (0x9C2031FD), (0xD9121A8C), (0xF51A96FD), (0x45CCBEBF), (0x5F884871), (0x66DAE291), (0x95ADC9E5),
//(0x33CF304D), (0x23C87DCE), (0xA7014FB3), (0xFB351E71), (0x8D60F66F), (0xBFD6DDD5), (0x151FE6F1), (0xDCC7935A), (0x0CACFBF0), (0xEE55A371), (0xBE46B755), (0x0F79DCD2), (0x23DFF76D), (0x83DE76D7), (0x8B0E3AEE), (0xB01BFB44), (0x4E3619FA), (0xA19F9E64),
//(0x549CB16C), (0x74649E69), (0xD8D48ADE), (0x1253AEDD), (0x80410FD2), (0x5B36889D), (0x880CE1E7), (0x56C498E7), (0xBD492AD9), (0x0899E45F), (0x8156E41D), (0x5DA539BA), (0x0AF35B2C), (0xDB89BAAE), (0xA128C966), (0xA02CEAEE), (0x8B7FBCEC), (0x9DAF7EF5),
//(0x7E3A79F1), (0x9C896ACD), (0x628F249A), (0xDD6E51AF), (0xAF73994B), (0x5966995D), (0xB7620597), (0x369B1E54), (0x7CEDC787), (0xE7B15B1F), (0xB36F5F7D), (0xB5B73EF6), (0xD76EEEE2), (0x14E86186), (0xCC159291), (0x4EC85CA3), (0xEC3C704E), (0x0B6633B3),
//(0x4D992D4B), (0xEB1B5774), (0xDFB9D508), (0x11783719), (0x9D7048E5), (0xC124569E), (0xCCB0E111), (0x15B2BB86), (0x18371D9C), (0xB2ABD94D), (0x8EC656B3), (0xBF010995), (0x465220BB), (0xC66E1FFC), (0x2C61D356), (0xAFFB176D), (0x88067E23), (0x3796F008),
//(0xC8D128F3), (0xB661A5FA), (0xF19CE4F0), (0x3F7D0345), (0xE97E6030), (0x4ABA91CF), (0xDEEDAB0C), (0x35FD1D77), (0xDD636439), (0x2F8B794C), (0x764E1D32), (0x7A8D0B1B), (0x8EACB27F), (0x577AD67B), (0x2B3FE2C5), (0x8ACD024A), (0xEDF5C42C), (0xF1CA951C),
//(0xB98B6D04), (0x6D12B864), (0x50E74D08), (0x73D1503B), (0xC7D3F6A9), (0x1F365E97), (0xE1340E59), (0x89D21B9D), (0x9B756733), (0x9024CD50), (0xE2C7FF52), (0xD9F061EF), (0x77151392), (0xA0E4A004), (0x60BE820F), (0xC7603BAF), (0xA62E3DA9), (0xA400E11F),
//(0x155111D4), (0x9030A374), (0x14185124), (0x8D043A00), (0x50945258), (0x20A6653E), (0x820D0208), (0x02028207), (0x0100FEF7), (0x000000E0), (0x00000000), (0x00000007), (0x00000038), (0x00000000), (0x00000038), (0x00010000), (0x00000000), (0x00000000),
//(0x00000000), (0x00000000), (0x00000000), (0x00000000), (0x54442D18), (0x401921FB), (0x86B2A52E), (0xBDA8BD59), (0x60DDDC8C), (0x3E21EE3B)
//};
