#ifndef DECODER_IMPL_H
#define DECODER_IMPL_H

#include "aacdecoder_lib.h"
#include "test_utils.h"
#include "stdint.h"

enum BAConcealMethod {
    CONCEAL_METHOD_SPECTRAL_MUTING      =  0,
    CONCEAL_METHOD_NOISE_SUBSTITUTION   =  1,
    CONCEAL_METHOD_ENERGY_INTERPOLATION =  2,
    CONCEAL_METHOD_NB,
};

struct DecoderContext {
    HANDLE_AACDECODER handle = nullptr;
    uint8_t* decoder_buffer = nullptr;
    int decoder_buffer_size = 8192;
    uint8_t* input_buffer = nullptr;
    int input_buffer_size = 8192;
    // uint8_t *anc_buffer;
    int conceal_method = 0;
    int drc_level = 0;
    int drc_boost = 0;
    int drc_heavy = 0;
    int drc_effect = 0;
    int drc_cut = 0;
    int level_limit = 0;
    int sample_rate = 44100;
    int frame_size = 1024;
    int channels = 1;
    int output_delay = 0;
};

int AudioX_fdk_aac_decode_close(DecoderContext *dec_ctx);

int AudioX_fdk_aac_decode_init(DecoderContext *dec_ctx);

int AudioX_fdk_aac_decode_frame(DecoderContext *dec_ctx, AudioStream *encoded_stream, AudioFrame *decoded_pcm_frame);

#endif