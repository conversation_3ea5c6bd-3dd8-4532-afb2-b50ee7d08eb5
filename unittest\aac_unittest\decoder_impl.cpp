#include "decoder_impl.h"
#include "stdio.h"
#include <cstring>

#ifdef AACDECODER_LIB_VL0
#define FDKDEC_VER_AT_LEAST(vl0, vl1) \
    ((AACDECODER_LIB_VL0 > vl0) || \
     (AACDECODER_LIB_VL0 == vl0 && AACDECODER_LIB_VL1 >= vl1))
#else
#define FDKDEC_VER_AT_LEAST(vl0, vl1) 0
#endif

#if !FDKDEC_VER_AT_LEAST(2, 5) // < 2.5.10
#define AAC_PCM_MAX_OUTPUT_CHANNELS AAC_PCM_OUTPUT_CHANNELS
#endif

#define DMX_ANC_BUFFSIZE       128
#define DECODER_MAX_CHANNELS     8
#define DECODER_BUFFSIZE      2048 * sizeof(INT_PCM)

static int AudioX_get_stream_info(DecoderContext *dec_ctx)
{
    CStreamInfo *info     = AudioX_aacDecoder_GetStreamInfo(dec_ctx->handle);
    int channel_counts[0x24] = { 0 };
    int i, ch_error       = 0;
    uint64_t ch_layout    = 0;

    if (!info) {
        fprintf(stderr, "Unable to get stream info\n");
        return -1;
    }

    if (info->sampleRate <= 0) {
        fprintf(stderr, "Stream info not initialized\n");
        return -1;
    }
    dec_ctx->sample_rate = info->sampleRate;
    dec_ctx->frame_size  = info->frameSize;
#if FDKDEC_VER_AT_LEAST(2, 5) // 2.5.10
    dec_ctx->output_delay    = info->outputDelay;
#endif

#if 0
    for (i = 0; i < info->numChannels; i++) {
        AUDIO_CHANNEL_TYPE ctype = info->pChannelType[i];
        if (ctype <= ACT_NONE || ctype >= FF_ARRAY_ELEMS(channel_counts)) {
            av_log(avctx, AV_LOG_WARNING, "unknown channel type\n");
            break;
        }
        channel_counts[ctype]++;
    }
    av_log(avctx, AV_LOG_DEBUG,
           "%d channels - front:%d side:%d back:%d lfe:%d top:%d\n",
           info->numChannels,
           channel_counts[ACT_FRONT], channel_counts[ACT_SIDE],
           channel_counts[ACT_BACK],  channel_counts[ACT_LFE],
           channel_counts[ACT_FRONT_TOP] + channel_counts[ACT_SIDE_TOP] +
           channel_counts[ACT_BACK_TOP]  + channel_counts[ACT_TOP]);

    switch (channel_counts[ACT_FRONT]) {
    case 4:
        ch_layout |= AV_CH_LAYOUT_STEREO | AV_CH_FRONT_LEFT_OF_CENTER |
                     AV_CH_FRONT_RIGHT_OF_CENTER;
        break;
    case 3:
        ch_layout |= AV_CH_LAYOUT_STEREO | AV_CH_FRONT_CENTER;
        break;
    case 2:
        ch_layout |= AV_CH_LAYOUT_STEREO;
        break;
    case 1:
        ch_layout |= AV_CH_FRONT_CENTER;
        break;
    default:
        av_log(avctx, AV_LOG_WARNING,
               "unsupported number of front channels: %d\n",
               channel_counts[ACT_FRONT]);
        ch_error = 1;
        break;
    }
    if (channel_counts[ACT_SIDE] > 0) {
        if (channel_counts[ACT_SIDE] == 2) {
            ch_layout |= AV_CH_SIDE_LEFT | AV_CH_SIDE_RIGHT;
        } else {
            av_log(avctx, AV_LOG_WARNING,
                   "unsupported number of side channels: %d\n",
                   channel_counts[ACT_SIDE]);
            ch_error = 1;
        }
    }
    if (channel_counts[ACT_BACK] > 0) {
        switch (channel_counts[ACT_BACK]) {
        case 3:
            ch_layout |= AV_CH_BACK_LEFT | AV_CH_BACK_RIGHT | AV_CH_BACK_CENTER;
            break;
        case 2:
            ch_layout |= AV_CH_BACK_LEFT | AV_CH_BACK_RIGHT;
            break;
        case 1:
            ch_layout |= AV_CH_BACK_CENTER;
            break;
        default:
            av_log(avctx, AV_LOG_WARNING,
                   "unsupported number of back channels: %d\n",
                   channel_counts[ACT_BACK]);
            ch_error = 1;
            break;
        }
    }
    if (channel_counts[ACT_LFE] > 0) {
        if (channel_counts[ACT_LFE] == 1) {
            ch_layout |= AV_CH_LOW_FREQUENCY;
        } else {
            av_log(avctx, AV_LOG_WARNING,
                   "unsupported number of LFE channels: %d\n",
                   channel_counts[ACT_LFE]);
            ch_error = 1;
        }
    }
    if (!ch_error &&
        av_get_channel_layout_nb_channels(ch_layout) != info->numChannels) {
        av_log(avctx, AV_LOG_WARNING, "unsupported channel configuration\n");
        ch_error = 1;
    }
    if (ch_error)
        avctx->channel_layout = 0;
    else
        avctx->channel_layout = ch_layout;
#endif
    dec_ctx->channels = info->numChannels;

    return 0;
}

int AudioX_fdk_aac_decode_close(DecoderContext *dec_ctx)
{
    if (dec_ctx->handle)
        AudioX_aacDecoder_Close(dec_ctx->handle);
    if (dec_ctx->input_buffer)
        free(dec_ctx->input_buffer);
    if (dec_ctx->decoder_buffer)
        free(dec_ctx->decoder_buffer);
    return 0;
}

int AudioX_fdk_aac_decode_init(DecoderContext *dec_ctx)
{
    AAC_DECODER_ERROR err;

    // default adts format
    dec_ctx->handle = AudioX_aacDecoder_Open(TT_MP4_ADTS, 1);
    if (!dec_ctx->handle) {
        fprintf(stderr, "Error opening decoder\n");
        return -1;
    }

#if 0
    if (avctx->extradata_size) {
        if ((err = AudioX_aacDecoder_ConfigRaw(s->handle, &avctx->extradata,
                                        &avctx->extradata_size)) != AAC_DEC_OK) {
            fprintf(stderr, "Unable to set extradata\n");
            return AVERROR_INVALIDDATA;
        }
    }
#endif

    if ((err = AudioX_aacDecoder_SetParam(dec_ctx->handle, AAC_CONCEAL_METHOD,
                                   dec_ctx->conceal_method)) != AAC_DEC_OK) {
        fprintf(stderr, "Unable to set error concealment method\n");
        return -1;
    }

    // set output channel
    if ((err = AudioX_aacDecoder_SetParam(dec_ctx->handle, AAC_PCM_MAX_OUTPUT_CHANNELS, dec_ctx->channels)) !=
        AAC_DEC_OK) {
        fprintf(stderr, "Unable to set fdkaac maxoutput channel\n");
        return -1;
    }

#if 0
    if (avctx->request_channel_layout > 0 &&
        avctx->request_channel_layout != AV_CH_LAYOUT_NATIVE) {
        int downmix_channels = -1;

        switch (avctx->request_channel_layout) {
        case AV_CH_LAYOUT_STEREO:
        case AV_CH_LAYOUT_STEREO_DOWNMIX:
            downmix_channels = 2;
            break;
        case AV_CH_LAYOUT_MONO:
            downmix_channels = 1;
            break;
        default:
            av_log(avctx, AV_LOG_WARNING, "Invalid request_channel_layout\n");
            break;
        }

        if (downmix_channels != -1) {
            if (AudioX_aacDecoder_SetParam(s->handle, AAC_PCM_MAX_OUTPUT_CHANNELS,
                                    downmix_channels) != AAC_DEC_OK) {
               av_log(avctx, AV_LOG_WARNING, "Unable to set output channels in the decoder\n");
            } else {
               s->anc_buffer = av_malloc(DMX_ANC_BUFFSIZE);
               if (!s->anc_buffer) {
                   fprintf(stderr, "Unable to allocate ancillary buffer for the decoder\n");
                   return AVERROR(ENOMEM);
               }
               if (AudioX_aacDecoder_AncDataInit(s->handle, s->anc_buffer, DMX_ANC_BUFFSIZE)) {
                   fprintf(stderr, "Unable to register downmix ancillary buffer in the decoder\n");
                   return AVERROR_UNKNOWN;
               }
            }
        }
    }

    if (s->drc_boost != -1) {
        if (AudioX_aacDecoder_SetParam(s->handle, AAC_DRC_BOOST_FACTOR, s->drc_boost) != AAC_DEC_OK) {
            fprintf(stderr, "Unable to set DRC boost factor in the decoder\n");
            return AVERROR_UNKNOWN;
        }
    }

    if (s->drc_cut != -1) {
        if (AudioX_aacDecoder_SetParam(s->handle, AAC_DRC_ATTENUATION_FACTOR, s->drc_cut) != AAC_DEC_OK) {
            fprintf(stderr, "Unable to set DRC attenuation factor in the decoder\n");
            return AVERROR_UNKNOWN;
        }
    }

    if (s->drc_level != -1) {
        // This option defaults to -1, i.e. not calling
        // AudioX_aacDecoder_SetParam(AAC_DRC_REFERENCE_LEVEL) at all, which defaults
        // to the level from DRC metadata, if available. The user can set
        // -drc_level -2, which calls AudioX_aacDecoder_SetParam(
        // AAC_DRC_REFERENCE_LEVEL) with a negative value, which then
        // explicitly disables the feature.
        if (AudioX_aacDecoder_SetParam(s->handle, AAC_DRC_REFERENCE_LEVEL, s->drc_level) != AAC_DEC_OK) {
            fprintf(stderr, "Unable to set DRC reference level in the decoder\n");
            return AVERROR_UNKNOWN;
        }
    }

    if (s->drc_heavy != -1) {
        if (AudioX_aacDecoder_SetParam(s->handle, AAC_DRC_HEAVY_COMPRESSION, s->drc_heavy) != AAC_DEC_OK) {
            fprintf(stderr, "Unable to set DRC heavy compression in the decoder\n");
            return AVERROR_UNKNOWN;
        }
    }

#if FDKDEC_VER_AT_LEAST(2, 5) // 2.5.10
    // Setting this parameter to -1 enables the auto behaviour in the library.
    if (AudioX_aacDecoder_SetParam(s->handle, AAC_PCM_LIMITER_ENABLE, s->level_limit) != AAC_DEC_OK) {
        fprintf(stderr, "Unable to set in signal level limiting in the decoder\n");
        return AVERROR_UNKNOWN;
    }
#endif

#if FDKDEC_VER_AT_LEAST(3, 0) // 3.0.0
    if (s->drc_effect != -1) {
        if (AudioX_aacDecoder_SetParam(s->handle, AAC_UNIDRC_SET_EFFECT, s->drc_effect) != AAC_DEC_OK) {
            fprintf(stderr, "Unable to set DRC effect type in the decoder\n");
            return AVERROR_UNKNOWN;
        }
    }
#endif
    avctx->sample_fmt = AV_SAMPLE_FMT_S16;

    s->decoder_buffer_size = DECODER_BUFFSIZE * DECODER_MAX_CHANNELS;
    s->decoder_buffer = av_malloc(s->decoder_buffer_size);
    if (!s->decoder_buffer)
        return AVERROR(ENOMEM);
#endif
    dec_ctx->input_buffer = (uint8_t*) malloc(dec_ctx->input_buffer_size);

    return 0;
}

int AudioX_fdk_aac_decode_frame(DecoderContext *dec_ctx, AudioStream *encoded_stream, AudioFrame *decoded_pcm_frame)
{
    int ret;
    AAC_DECODER_ERROR err;
    UINT valid = encoded_stream->encoded_len;
    uint32_t enc_len = static_cast<uint32_t>(encoded_stream->encoded_len);
    memcpy(dec_ctx->input_buffer, encoded_stream->encoded_data, encoded_stream->encoded_len);

    err = AudioX_aacDecoder_Fill(dec_ctx->handle, &dec_ctx->input_buffer, &enc_len, &valid);
    if (err != AAC_DEC_OK) {
        fprintf(stderr, "aacDecoder_Fill() failed: %x\n", err);
        return -1;
    }

    err = AudioX_aacDecoder_DecodeFrame(dec_ctx->handle, decoded_pcm_frame->pcm_data, 8192*2 / sizeof(INT_PCM), 0);
    if (err == AAC_DEC_NOT_ENOUGH_BITS) {
        ret = enc_len - valid;
        goto end;
    }
    if (err != AAC_DEC_OK) {
        fprintf(stderr,
               "AudioX_aacDecoder_DecodeFrame() failed: %x\n", err);
        ret = -1;
        goto end;
    }

    if ((ret = AudioX_get_stream_info(dec_ctx)) < 0)
        goto end;
    decoded_pcm_frame->samples_per_channel = dec_ctx->frame_size;
    decoded_pcm_frame->channels = dec_ctx->channels;

#if 0
    if ((ret = ff_get_buffer(avctx, frame, 0)) < 0)
        goto end;

    if (frame->pts != AV_NOPTS_VALUE)
        frame->pts -= av_rescale_q(s->output_delay,
                                   (AVRational){1, avctx->sample_rate},
                                   avctx->time_base);

    memcpy(frame->extended_data[0], s->decoder_buffer,
           avctx->channels * avctx->frame_size *
           av_get_bytes_per_sample(avctx->sample_fmt));
#endif
    
    ret = encoded_stream->encoded_len - valid;

end:
    return ret;
}

int AudioX_fdk_aac_decode_flush(DecoderContext *dec_ctx)
{
    AAC_DECODER_ERROR err;

    if (!dec_ctx->handle)
        return -1;

    if ((err = AudioX_aacDecoder_SetParam(dec_ctx->handle,
                                   AAC_TPDEC_CLEAR_BUFFER, 1)) != AAC_DEC_OK) {
        fprintf(stderr, "failed to clear buffer when flushing\n");
        return -1;
    }

    return 0;
}
