# coding=utf-8

import argparse
import os
import wave
from multiprocessing import Pool, cpu_count
from datetime import datetime
import subprocess
from subprocess import Popen
import re
import pandas as pd

ENCODE_OK = 0
ENCODE_ERROR = 1
UNSUPPORTED_ENCODE_FORMAT = 2

class PeaqRefScore():
    def __init__(self, ref_csv_file):
        if not os.path.isfile(ref_csv_file):
            print('ref score csv file not exist.')
            exit(1)
        self.ref_data_frame = pd.read_csv(ref_csv_file)
    
    def get_ref_score_dict(self, wav_name, aot, vbr, bitrate):
        res_dict = {'valid':False, 'ref_score':-1}
        if wav_name not in self.ref_data_frame['file'].values:
            return res_dict
        wav_name_data = self.ref_data_frame[self.ref_data_frame.file==wav_name].iloc[0]
        key_name = 'aot{}_vbr{}_bitrate{}'.format(aot, vbr, bitrate)
        if key_name in wav_name_data:
            res_dict['valid'] = True
            res_dict['ref_score'] = wav_name_data[key_name]
            return res_dict
        else:
            return res_dict

class EncodeConfig:
    def __init__(self, aot = 2, bitrate = 64000, vbr = 0):
        self.aot = aot
        self.bitrate = bitrate
        self.vbr = vbr
    
    def to_dict(self):
        res = {'aot':self.aot, 'bitrate':self.bitrate, 'vbr':self.vbr}
        return res

def load_args():
    parser = argparse.ArgumentParser(description = 'The script is used to test aac encode quality.')
    parser.add_argument('--input_wav_dir', required = True, help = 'Input wav directory.')
    parser.add_argument('--aac_demo_bin_path', required = False, default='./aac-enc', help = 'aac-enc demo bin path.')
    parser.add_argument('--peaq_bin_path', required = False, default = './PQevalAudio', help = 'Peaq tool bin path.')
    parser.add_argument('--test_bitrate', required = False, default = [64000], nargs = '+', type = int, help = 'Test bitrate.')
    parser.add_argument('--test_aot', required = False, default = [2, 5, 29], nargs = '+', type = int, help = 'Test aot.')
    parser.add_argument('--test_vbr', required = False, default = [0], nargs = '+', type = int, help = 'Test vbr mode.')
    parser.add_argument('--test_bitrate_mode', required = False, default = [0, 2], nargs = '+', type = int, help = 'Test bitrate mode.')
    parser.add_argument('--ref_score_file', required = False, default='./ref_score.csv', help = 'Peaq ref score file path.')
    args = parser.parse_args()
    return args

def get_file_path_list(input_dir, extension=''):
    file_list = os.listdir(input_dir)
    res = []
    for i, wav_file in enumerate(file_list):
        wav_file_path = '{}/{}'.format(input_dir, wav_file)
        if os.path.isfile(wav_file_path) and wav_file.endswith(extension):
            res.append(wav_file_path)
            print('file_index:', i, 'file_path:', wav_file_path)
    return res

def generate_encode_cmd(encode_bin_path, encoder_config, \
                        in_audio_path, encoded_aac_file_apth, decoded_wav_file_path):
    cmd = '{0} -v {1} -r {2} -t {3} -f 1 {4} {5} {6}' \
    .format(encode_bin_path, \
            encoder_config.vbr, encoder_config.bitrate, encoder_config.aot, \
            in_audio_path, encoded_aac_file_apth, decoded_wav_file_path)
    return cmd

def gen_file_suffix(encoder_config):
    res = 'aot{}_bitrate{}_vbr{}' \
    .format(encoder_config.aot, encoder_config.bitrate, encoder_config.vbr)
    return res

def aac_encode(encode_bin_path, peaq_bin_path, input_wav_path, encoder_config, timestamp=''):
    wav = wave.open(input_wav_path)
    if wav.getnchannels() == 1 and encoder_config.aot == 29:
        return UNSUPPORTED_ENCODE_FORMAT
    output_wav_dir = '{}/gen_{}/wav'.format(os.path.abspath(os.path.dirname(os.path.dirname(input_wav_path))), timestamp)
    os.makedirs(output_wav_dir, exist_ok = True)
    output_aac_dir = '{}/gen_{}/aac'.format(os.path.abspath(os.path.dirname(os.path.dirname(input_wav_path))), timestamp)
    os.makedirs(output_aac_dir, exist_ok = True)
    wav_basename = os.path.basename(input_wav_path).split('.')[0]
    output_suffix = gen_file_suffix(encoder_config)
    output_aac_file_path = '{}/{}_{}.aac'.format(output_aac_dir, wav_basename, output_suffix)
    output_wav_file_path = '{}/{}_{}.wav'.format(output_wav_dir, wav_basename, output_suffix)
    enc_cmd = generate_encode_cmd(encode_bin_path, encoder_config, \
                                  os.path.abspath(input_wav_path), output_aac_file_path, output_wav_file_path)
    res_dict = {'enc_cmd':None, \
                'input_wav_abs_path':None, 'output_aac_abs_path':None, 'output_wav_abs_path':None, \
                'peaq_cmd': None, 'peaq_score':None,
                'encode_config':None,\
                'encode_wav':None}
    res_dict['enc_cmd'] = enc_cmd
    res_dict['input_wav_abs_path'] = os.path.abspath(input_wav_path)
    res_dict['output_aac_abs_path'] = output_aac_file_path
    res_dict['output_wav_abs_path'] = output_wav_file_path
    res_dict['encode_config'] = encoder_config.to_dict()
    res_dict['encode_wav'] = os.path.basename(input_wav_path)
    subprocess.run(enc_cmd, shell=True, capture_output=False, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    peaq_res = get_peaq_score(peaq_bin_path, os.path.abspath(input_wav_path), output_wav_file_path, wav.getframerate())
    res_dict['peaq_cmd'] = peaq_res['peaq_cmd']
    res_dict['peaq_score'] = peaq_res['peaq_score']
    return res_dict



def get_peaq_score(peaq_bin_path, ref_wav_path, degraded_wav_path, samplerate=48000):
    peaq_score = -1
    peaq_ref_path = ref_wav_path
    peaq_degraded_path = degraded_wav_path
    tmp_degraded_wav_name = '{}_tmp_48khz.wav'.format(os.path.basename(degraded_wav_path).split('.')[0])
    tmp_degraded_ref_name = '{}_tmp_48khz_ref.wav'.format(os.path.basename(degraded_wav_path).split('.')[0])
    tmp_degraded_wav_path = '{}/{}'.format(os.path.dirname(degraded_wav_path), tmp_degraded_wav_name)
    tmp_ref_wav_path = '{}/{}'.format(os.path.dirname(degraded_wav_path), tmp_degraded_ref_name)
    sox_resample_degraded_wav_cmd = 'sox -r 48000 {} {}'.format(degraded_wav_path, tmp_degraded_wav_path)
    sox_resample_ref_wav_cmd = 'sox -r 48000 {} {}'.format(ref_wav_path, tmp_ref_wav_path)
    subprocess.run(sox_resample_degraded_wav_cmd, shell=True, capture_output=False, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    subprocess.run(sox_resample_ref_wav_cmd, shell=True, capture_output=False, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    peaq_degraded_path = tmp_degraded_wav_path
    peaq_ref_path = tmp_ref_wav_path
    peaq_cmd = '{} {} {}'.format(peaq_bin_path, peaq_ref_path, peaq_degraded_path)
    try:
        p = Popen(peaq_cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        stdout, stderr = p.communicate()
        peaq_score = float(re.findall(r'Objective Difference Grade:(.*?)\n', stdout.decode('utf-8'))[0])
        peaq_score = peaq_score + 5.0
    except:
        pass
    res_dict = {'peaq_cmd':peaq_cmd, 'peaq_score':peaq_score}
    return res_dict

def get_test_conditions(aot_list, vbr_list, bitrate_list):
    res = []
    for aot in aot_list:
        for vbr in vbr_list:
            if vbr > 0:
                param_dict = {'aot':2, 'vbr':5, 'bitrate':64000, 'bitrate_mode':0}
                param_dict['aot'] = aot
                param_dict['vbr'] = vbr
                res.append(param_dict)
            else:
                for bitrate in bitrate_list:
                    param_dict = {'aot':2, 'vbr':5, 'bitrate':64000, 'bitrate_mode':0}
                    param_dict['aot'] = aot
                    param_dict['vbr'] = vbr
                    param_dict['bitrate'] = bitrate
                    res.append(param_dict)
    # print('test conditions:', res)
    return res

def get_analyse_res(analysis_dict_list, peaq_ref_class):
    new_data = pd.DataFrame(columns=['file', 'aot', 'vbr', 'bitrate', 'peaq_score', 'ref_peaq_valid', 'ref_peaq_score'])
    for analysis_dict in analysis_dict_list:
        new_data.loc[len(new_data)] = [None] * len(new_data.columns)
        new_data.loc[len(new_data)-1, 'file'] = analysis_dict['encode_wav']
        new_data.loc[len(new_data)-1, 'peaq_score'] = analysis_dict['peaq_score']
        new_data.loc[len(new_data)-1, 'aot'] = analysis_dict['encode_config']['aot']
        new_data.loc[len(new_data)-1, 'vbr'] = analysis_dict['encode_config']['vbr']
        new_data.loc[len(new_data)-1, 'bitrate'] = analysis_dict['encode_config']['bitrate']
        # new_data.loc[len(new_data)-1, 'bitrate_mode'] = analysis_dict['encode_config']['bitrate_mode']
        ref_score_dict = peaq_ref_class.get_ref_score_dict(analysis_dict['encode_wav'], 
                                                    analysis_dict['encode_config']['aot'],
                                                    analysis_dict['encode_config']['vbr'],
                                                    analysis_dict['encode_config']['bitrate'])
        new_data.loc[len(new_data)-1, 'ref_peaq_score'] = ref_score_dict['ref_score']
        new_data.loc[len(new_data)-1, 'ref_peaq_valid'] = ref_score_dict['valid']
    return new_data

def encode_quality_test(args):
    if not os.path.exists(args.input_wav_dir):
        print('input dir not exists')
        exit(1)
    if not os.path.exists(args.aac_demo_bin_path):
        print('aac-demo bin not exists')
        exit(1)
    if not os.path.exists(args.peaq_bin_path):
        print('peaq bin not exists')
        exit(1)
    if not os.path.exists(args.ref_score_file):
        print('ref score file not exists')
        exit(1)
    apply_sync_ret_list = []
    ref_peaq_score_class = PeaqRefScore(args.ref_score_file)
    aac_demo_bin_path = os.path.abspath(args.aac_demo_bin_path)
    peaq_bin_path = os.path.abspath(args.peaq_bin_path)
    test_conditions = get_test_conditions(args.test_aot, args.test_vbr, args.test_bitrate)
    test_file_list = get_file_path_list(args.input_wav_dir, '.wav')
    timestamp = current_date = datetime.now().strftime('%Y-%m-%d-%H-%M')
    process_pool = Pool()
    for input_wav_path in test_file_list:
        for test_condition in test_conditions:
            test_encode_config = EncodeConfig()
            test_encode_config.aot = test_condition['aot']
            test_encode_config.vbr = test_condition['vbr']
            test_encode_config.bitrate = test_condition['bitrate']
            # test_encode_config.bitrate_mode = test_condition['bitrate_mode']
            print('processing:', input_wav_path, ' test_condition:', test_condition)
            ret = process_pool.apply_async(aac_encode, args = (aac_demo_bin_path, peaq_bin_path, input_wav_path, test_encode_config, timestamp,))
            apply_sync_ret_list.append(ret)
    process_pool.close()
    process_pool.join()
    analysis_list = []
    for item in apply_sync_ret_list:
        analysis_list.append(item.get())      
    analysis_pd = get_analyse_res(analysis_list, ref_peaq_score_class)
    print(analysis_pd)
    test_succeed_flag = True
    fail_case_list = []
    for index, row_data in analysis_pd.iterrows():
        if (row_data['ref_peaq_score'] - row_data['peaq_score'] > 0.2) and row_data['ref_peaq_valid'] == True:
            test_succeed_flag = False
            fail_case_list.append(row_data)

    print('{} cases failed:'.format(len(fail_case_list)))
    for fail_case in fail_case_list:
        print('file: {}'.format(fail_case['file']))
        print('aot: {}'.format(fail_case['aot']))
        print('vbr: {}'.format(fail_case['vbr']))
        print('bitrate: {}'.format(fail_case['bitrate']))
        # print('bitrate_mode: {}'.format(fail_case['bitrate_mode']))
        print('ref_peaq_score: {}'.format(fail_case['ref_peaq_score']))
        print('actual_peaq_score: {}'.format(fail_case['peaq_score']))
        print('\n')
    
    if test_succeed_flag == True:
        print('All test cases succeed.\n')
    else:
        print('Some cases failed, case num: {}.\n'.format(len(fail_case_list)))
        exit(1)



if __name__ == '__main__':
    print('\n\n\nStarting AAC UnitTest!')
    args = load_args()
    encode_quality_test(args)