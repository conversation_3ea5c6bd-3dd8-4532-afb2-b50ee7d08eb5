#ifndef ENCODER_IMPL_H
#define ENCODER_IMPL_H

#include "aacenc_lib.h"
#include "test_utils.h"
#include "stdint.h"

struct EncoderContext {
    HANDLE_AACENCODER handle = nullptr;
    AUDIO_OBJECT_TYPE aot = AOT_AAC_LC;
    TRANSPORT_TYPE transtype = TT_MP4_ADTS;
    int samplerate = 44100;
    int channels = 2;
    int afterburner = 1;
    int eld_sbr = 0;
    int eld_v2 = 0;
    int signaling = -1;
    int latm = 0;
    int header_period = 0;
    int vbr = 0;
    int bitrate_mode = 0;
    int bitrate = 64000;
    int abr = 0;
    int cutoff_freq = -1;
    int frame_size = -1;
};


AACENC_ERROR AudioX_aac_encode_init(EncoderContext *avctx);

AACENC_ERROR AudioX_aac_encode_frame(EncoderContext *enc_ctx, AudioFrame *pcm_frame, AudioStream *encoded_stream);

const char *AudioX_aac_get_error(AACENC_ERROR err);

int AudioX_aac_encode_close(EncoderContext *enc_ctx);


#endif