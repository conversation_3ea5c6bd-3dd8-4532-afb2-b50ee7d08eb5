/* ------------------------------------------------------------------
 * Copyright (C) 2011 <PERSON>
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *	  http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either
 * express or implied.
 * See the License for the specific language governing permissions
 * and limitations under the License.
 * -------------------------------------------------------------------
 */

#include <stdio.h>
#include <stdint.h>
#include <string.h>
#include "time.h"
#include <errno.h>
#include <chrono>
#include <iostream>
#include <algorithm>
#if defined(_MSC_VER)
#include <getopt.h>
#else
#include <unistd.h>
#endif

#include <stdlib.h>
#include <string.h>
#include "wavreader.h"
#include "wavwriter.h"
#include <AudioToolbox/AudioToolbox.h>

#ifdef FDKAAC_DEMO_IOS
#include "device_helpers/ios/helpers.h"
#endif

#define KDefaultCodecSampleRate 48000
#define KDefaulCodecChannels 1
#define kMaxDecodeFramesize 11520
#define kMaxFramesize 3*kMaxDecodeFramesize
// audio buffer for decoded data and stretched data
class RXAudioPcmBuffer {
public:
    // creates a new audio pcm buffer
    RXAudioPcmBuffer(int32_t sr = KDefaultCodecSampleRate, int32_t channels = KDefaulCodecChannels,
                     uint32_t size = kMaxDecodeFramesize)
            : sample_rate_(sr),
              channel_num_(channels),
              max_size_(size < kMaxFramesize ? size : kMaxFramesize),
              actual_size_(0),
              data_(new int16_t[max_size_]),
              is_muted_data_(false) {
        if (data_.get()) {
            memset(data_.get(), 0x00, sizeof(int16_t) * max_size_);
        }
    }

    ~RXAudioPcmBuffer() = default;

    // get actual size of buffer, is less than max_size
    void set_actual_size(uint32_t size) {
        actual_size_ = (size <= max_size_) ? size : max_size_;
    }

    // set actual size of buffer
    uint32_t get_actual_size() {
        return actual_size_;
    }

    // get max size of buffer
    uint32_t get_max_size() {
        return max_size_;
    }

    void set_rate_and_channels(int32_t sr, int32_t channels) {
        sample_rate_ = sr;
        channel_num_ = channels;
    }

    int32_t get_sample_rate() {
        return sample_rate_;
    }

    int32_t get_channel_num() {
        return channel_num_;
    }

    void clear() {
        set_actual_size(0);
        is_muted_data_ = true;
        // memset(data_.get(), 0x00, sizeof(int16_t) * max_size_);
    }

    void add_actual_size(uint32_t size) {
        actual_size_ += size;
        actual_size_ = (actual_size_ > max_size_) ? (max_size_) : actual_size_;
    }

    void sub_actual_size(uint32_t size) {
        actual_size_ = (actual_size_ > size) ? (actual_size_ - size) : 0;
    }

    uint32_t get_memroy_left() const {
        return max_size_ - actual_size_;
    }

    static const int16_t* zero_data() {
        static int16_t* null_data = new int16_t[kMaxFramesize]();
        return &null_data[0];
    }

    const int16_t* data_buffer() const {
        return is_muted_data_ ? zero_data() : data_.get();
    }

    int16_t* data_buffer_w() const {
        return data_.get() + actual_size_;
    }

    void set_mute(bool mute) {
        is_muted_data_ = mute;
    }

    bool is_mute() const {
        return is_muted_data_;
    }

    bool resize(uint32_t new_size) {
        if (new_size == max_size_) {
            return false;
        }

        if (new_size > kMaxFramesize) {
            return false;
        }

        std::unique_ptr<int16_t[]> data_new(new int16_t[new_size]);
        if (!data_new) {
            return false;
        }

        if (new_size >= actual_size_) {
            memcpy(data_new.get(), data_.get(), actual_size_ * sizeof(int16_t));
        } else {
            memcpy(data_new.get(), data_.get(), new_size * sizeof(int16_t));
            actual_size_ = new_size;
        }

        max_size_ = new_size;
        data_ = std::move(data_new);

        return true;
    }

private:
    int32_t sample_rate_;
    int32_t channel_num_;
    uint32_t max_size_;
    uint32_t actual_size_;
    std::unique_ptr<int16_t[]> data_;
    bool is_muted_data_;
};

class RXAudioFillBuffer {
public:
    RXAudioFillBuffer(int32_t max_size) {
        data_ = new uint8_t[max_size];
        max_size_ = max_size;
        read_pos_ = write_pos_ = 0;
    }

    ~RXAudioFillBuffer() {
        if (data_) {
            delete[] data_;
        }
    }

    RXAudioFillBuffer(const RXAudioFillBuffer&) = delete;
    RXAudioFillBuffer& operator=(const RXAudioFillBuffer&) = delete;

    void write_data(uint8_t* data, int32_t length) {
        if (write_pos_ + length > max_size_) {
            memcpy(data_ + write_pos_, data, max_size_ - write_pos_);
            memcpy(data_, data + max_size_ - write_pos_, length - max_size_ + write_pos_);
        } else {
            memcpy(data_ + write_pos_, data, length);
        }

        write_pos_ = (write_pos_ + length) % max_size_;
    }

    int32_t avaiable_read_len() {
        return (write_pos_ + max_size_ - read_pos_) % max_size_;
    }

    uint8_t* read_data(int32_t length) {
        uint8_t* read_addr = data_ + read_pos_;
        read_pos_ = (read_pos_ + length) % max_size_;
        return read_addr;
    }

    void clear() {
        if (data_) {
            memset(data_, 0x00, max_size_);
        }
        read_pos_ = write_pos_ = 0;
    }

private:
    uint8_t* data_ = nullptr;
    int32_t max_size_;
    int32_t read_pos_;
    int32_t write_pos_;
};

struct DecoderType {
    int32_t sample_rate;
    int32_t channels_num;
    int32_t aac_aot;
};

struct UserData {
    int32_t channels;
    AudioStreamPacketDescription* pd;
    int32_t frame_size;
    RXAudioFillBuffer* fill_buffer;
};

struct DecUserData {
    uint32_t channels;
    void* data;
    uint32_t data_bytes;
    AudioStreamPacketDescription pkt_desc_;
};


typedef enum {
    kRXAOT_NONE = -1,
    kRXAOT_AAC_LC = 2, /**< Low Complexity object                     */
    kRXAOT_SBR = 5,
    kRXAOT_PS = 29, /**< PS, Parametric Stereo (includes SBR)      */
} AudioAACAot;

static const int kNoMoreData = 1;
static const uint32_t MAX_AUDIO_FRAMES = 128;

static const int SamplingRateTable[] = {
        96000, 88200, 64000, 48000, 44100, 32000, 24000, 22050, 16000, 12000, 11025, 8000, 7350, 0, 0, 57600,
        51200, 40000, 38400, 34150, 28800, 25600, 20000, 19200, 17075, 14400, 12800, 9600, 0,    0, 0, 0};

static const int ChannelTable[] = {0, 1, 2, 3, 4, 5, 6, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0};

int getSamplingRateIndex(int sample_rate) {
    int sampleRateIndex = 0;
    int sampleRateTableSize = sizeof(SamplingRateTable) / sizeof(SamplingRateTable[0]);
    for (; sampleRateIndex < sampleRateTableSize && sampleRateIndex < 0xf; sampleRateIndex++) {
        if (sample_rate == SamplingRateTable[sampleRateIndex]) {
            break;
        }
    }

    return sampleRateIndex;
}

int getChannelIndex(int channel) {
    int channelIndex = 0;
    int channelTableSize = sizeof(ChannelTable) / sizeof(ChannelTable[0]);
    for (; channelIndex < channelTableSize; channelIndex++) {
        if (channel == ChannelTable[channelIndex]) {
            break;
        }
    }
    return channelIndex;
}

int rx_audio_gen_aac_asc(struct DecoderType dec_type, uint8_t* asc, int32_t len) {
    uint8_t aot, sr_idx, half_sr_idx, ch_mode;
    int invalid_len = 0;
    if (!asc) {
        return 0;
    }

    memset(asc, 0x00, len);

    if (20 == dec_type.aac_aot && dec_type.channels_num < 2) {
        return 0;
    }

    aot = dec_type.aac_aot;

    if (44100 == dec_type.sample_rate || 48000 == dec_type.sample_rate || 32000 == dec_type.sample_rate || 16000 == dec_type.sample_rate) {
        sr_idx = getSamplingRateIndex(dec_type.sample_rate);
        half_sr_idx = getSamplingRateIndex(dec_type.sample_rate>>1);
    } else {
        return 0;
    }

    ch_mode = getChannelIndex(dec_type.channels_num);

    if (2 == dec_type.aac_aot) {
        asc[0] = (aot << 3) | (sr_idx >> 1);
        asc[1] = (sr_idx << 7) | (ch_mode << 3);
        invalid_len = 2;
    } else if (5 == dec_type.aac_aot) {
        asc[0] = (aot << 3) | (half_sr_idx >> 1);
        asc[1] = (half_sr_idx << 7) | (ch_mode << 3) | (sr_idx >> 1);
        asc[2] = (sr_idx << 7) | (2 << 2);
        asc[3] = 0;
        invalid_len = 4;
    } else if (29 == dec_type.aac_aot) {
        asc[0] = (aot << 3) | (half_sr_idx >> 1);
        asc[1] = (half_sr_idx << 7) | (1 << 3) | (sr_idx >> 1);
        asc[2] = (sr_idx << 7) | (2 << 2);
        asc[3] = 0;
        invalid_len = 4;
    }

    return invalid_len;
}

static int64_t current_system_clock() {
    // count nano-seconds since system begin
    auto now_time = std::chrono::steady_clock::now();
    int64_t nano_time = std::chrono::duration_cast<std::chrono::nanoseconds>(now_time.time_since_epoch()).count();
    return nano_time;
}

uint32_t get_fromat_id(int aac_aot) {
    if (kRXAOT_SBR == aac_aot) {
        return kAudioFormatMPEG4AAC_HE;
    } else if (kRXAOT_PS == aac_aot) {
        return kAudioFormatMPEG4AAC_HE_V2;
    } else {
        return kAudioFormatMPEG4AAC;
    }
}

OSStatus IoProc(AudioConverterRef audioConverter, UInt32* ioNumDataPackets,
                                          AudioBufferList* ioData, AudioStreamPacketDescription** ioPacketDesc,
                                          void* inUserData) {
    UserData* ud = static_cast<UserData*>(inUserData);

    if (static_cast<unsigned long>(ud->fill_buffer->avaiable_read_len()) < ud->frame_size * ud->channels * sizeof(int16_t)) {
        *ioNumDataPackets = 0;
        return kNoMoreData;
    }

    *ioNumDataPackets = std::min(UInt32(ud->frame_size), *ioNumDataPackets);

    ioData->mNumberBuffers = 1;
    ioData->mBuffers[0].mDataByteSize = ud->frame_size * sizeof(int16_t) * ud->channels;
    ioData->mBuffers[0].mData = (void*)(ud->fill_buffer->read_data(ioData->mBuffers[0].mDataByteSize));
    ioData->mBuffers[0].mNumberChannels = ud->channels;

    return noErr;
}

OSStatus dec_io_proc_callback(AudioConverterRef audioConverter, UInt32* ioNumDataPackets,
                                                AudioBufferList* ioData, AudioStreamPacketDescription** ioPacketDesc,
                                                void* inUserData) {
    DecUserData* ud = static_cast<DecUserData*>(inUserData);

    if (0 == ud->data_bytes) {
        *ioNumDataPackets = 0;
        return 100;
    }

    if (ioPacketDesc) {
        ud->pkt_desc_.mStartOffset = 0;
        ud->pkt_desc_.mVariableFramesInPacket = 0;
        ud->pkt_desc_.mDataByteSize = ud->data_bytes;
        *ioPacketDesc = &(ud->pkt_desc_);
    }

    ioData->mNumberBuffers = 1;
    ioData->mBuffers[0].mNumberChannels = ud->channels;
    ioData->mBuffers[0].mData = ud->data;
    ioData->mBuffers[0].mDataByteSize = ud->data_bytes;

    ud->data_bytes = 0;

    return 0;
}

//static void int_to_char(uint32_t i, unsigned char ch[4])
//{
//    ch[0] = i>>24;
//    ch[1] = (i>>16)&0xFF;
//    ch[2] = (i>>8)&0xFF;
//    ch[3] = i&0xFF;
//}
//
//static uint32_t char_to_int(unsigned char ch[4])
//{
//    return ((uint32_t)ch[0]<<24) | ((uint32_t)ch[1]<<16)
//           | ((uint32_t)ch[2]<< 8) |  (uint32_t)ch[3];
//}

void usage(const char* name) {
    fprintf(stderr, "%s [-r bitrate] [-t aot] [-a afterburner] [-s sbr] [-v vbr] in.wav out.aac out.wav\n", name);
    fprintf(stderr, "Supported AOTs:\t\tvbr:\n");
    fprintf(stderr, "\t2\tAAC-LC\t\t\t0\tfor CBR(default)\n");
    fprintf(stderr, "\t5\tHE-AAC\t\t\t1\tfor vbr 1 about 32 kbps/channel\n");
    fprintf(stderr, "\t29\tHE-AAC v2\t\t2\tfor vbr 2 about 40 kbps/channel\n");
    fprintf(stderr, "\t23\tAAC-LD\t\t\t3\tfor vbr 3 about 48-56 kbps/channel\n");
    fprintf(stderr, "\t39\tAAC-ELD\t\t\t4\tfor vbr 4 about 64 kbps/channel\n");
}

int main(int argc, char *argv[]) {
    int bitrate = 64000;
    int ch;
    const char *infile, *bitstreamfile, *outfile;
    FILE *fin = NULL, *fout = NULL, *fbitstream = NULL;
    void *wav, *fout_wav;
    int format, sample_rate, channels, bits_per_sample;
    int input_size;
    uint8_t* input_buf;
    int16_t* convert_buf;
    int aot = 2;
    int afterburner = 1;
    int eld_sbr = 0;
    int vbr = 0;
    int complexity = 5;
    uint32_t max_encoded_bytes_ = 0;
    int enc_frame_samples = 0;
    std::unique_ptr<RXAudioFillBuffer> fill_buffer_;

    double enc_total_rtf_ns = 0.0;
    double dec_total_rtf_ns = 0.0;
    double enc_max_rtf = 0.0;
    double dec_max_rtf = 0.0;
    double framesize_ms = 0.0;
    double average_enc_rtf_ns, average_dec_rtf_ns;

#ifdef FDKAAC_DEMO_IOS
	std::string outfile_str, infile_str, bitstreamfile_str;
    std::string sandbox_dir = GetIOSDocumentsDirPath();
#endif

    AudioConverterRef atx_aac_enc_converter_ = nullptr;
    while ((ch = getopt(argc, argv, "r:t:a:s:v:")) != -1) {
        switch (ch) {
            case 'r':
                bitrate = atoi(optarg);
                break;
            case 't':
                aot = atoi(optarg);
                break;
            case 'a':
                afterburner = atoi(optarg);
                break;
            case 's':
                eld_sbr = atoi(optarg);
                break;
            case 'v':
                vbr = atoi(optarg);
                break;
            case '?':
            default:
                usage(argv[0]);
                return 1;
        }
    }
    if (argc - optind < 2) {
        usage(argv[0]);
        return 1;
    }
#ifdef FDKAAC_DEMO_IOS
	infile_str = sandbox_dir + std::string("/") + std::string(argv[optind]);
    infile = infile_str.c_str();
	bitstreamfile_str = sandbox_dir + std::string("/") + std::string(argv[optind+1]);
	bitstreamfile = bitstreamfile_str.c_str();
	outfile_str = sandbox_dir + std::string("/") + std::string(argv[optind+2]);
	outfile = outfile_str.c_str();
#else
    infile = argv[optind];
	bitstreamfile = argv[optind + 1];
    outfile = argv[optind + 2];
#endif

    wav = wav_read_open(infile);
    if (!wav) {
        fprintf(stderr, "Unable to open wav file %s\n", infile);
        return 1;
    }

    if (!wav_get_header(wav, &format, &channels, &sample_rate, &bits_per_sample, NULL)) {
        fprintf(stderr, "Bad wav file %s\n", infile);
        return 1;
    }
    if (format != 1) {
        fprintf(stderr, "Unsupported WAV format %d\n", format);
        return 1;
    }
    if (bits_per_sample != 16) {
        fprintf(stderr, "Unsupported WAV sample depth %d\n", bits_per_sample);
        return 1;
    }

    fout_wav = wav_write_open(outfile, sample_rate, bits_per_sample, channels);
    if (!fout_wav) {
        fprintf(stderr, "Unable to open output wav file %s\n", outfile);
        return 1;
    }

    // init encoder
    OSStatus status;
    UInt32 array_size;
    AudioStreamBasicDescription in_format;
    memset(&in_format, 0, sizeof(AudioStreamBasicDescription));
    AudioStreamBasicDescription out_format;
    memset(&out_format, 0, sizeof(AudioStreamBasicDescription));

    in_format.mSampleRate = sample_rate;
    in_format.mFormatID = kAudioFormatLinearPCM;
    in_format.mFormatFlags = kAudioFormatFlagIsSignedInteger | kAudioFormatFlagIsPacked;
    in_format.mBytesPerPacket = sizeof(int16_t) * channels;
    in_format.mFramesPerPacket = 1;
    in_format.mBytesPerFrame = sizeof(int16_t) * channels;
    in_format.mChannelsPerFrame = channels;
    in_format.mBitsPerChannel = 16;

    out_format.mSampleRate = sample_rate;
    out_format.mFormatID = get_fromat_id(aot);
    out_format.mChannelsPerFrame = in_format.mChannelsPerFrame;

    array_size = sizeof(out_format);
    status = AudioFormatGetProperty(kAudioFormatProperty_FormatInfo, 0, NULL, &array_size, &out_format);
    if (status) {
        return -1;
    }

    status = AudioConverterNew(&in_format, &out_format, &atx_aac_enc_converter_);
    if (status != 0) {
        return -1;
    }

    // set bitrate
    uint32_t rate = static_cast<uint32_t>(bitrate);
    UInt32 size;

#ifdef __MACH__
    int bitrate_mode;
    bitrate_mode = kAudioCodecBitRateControlMode_Constant;
    status = AudioConverterSetProperty(atx_aac_enc_converter_, kAudioCodecPropertyBitRateControlMode, sizeof(bitrate_mode),
                                       &bitrate_mode);
    if (status) {
        return -1;
    }
#endif

    status = AudioConverterGetPropertyInfo(atx_aac_enc_converter_, kAudioConverterApplicableEncodeBitRates, &size, NULL);
    if (!status && size) {
        UInt32 new_rate = rate;
        int count;
        int i;
        AudioValueRange* ranges = (AudioValueRange*)malloc(size);
        if (!ranges) {
            return -1;
        }

        AudioConverterGetProperty(atx_aac_enc_converter_, kAudioConverterApplicableEncodeBitRates, &size, ranges);
        count = size / sizeof(AudioValueRange);
        for (i = 0; i < count; i++) {
            AudioValueRange* range = &ranges[i];
            if (rate >= range->mMinimum && rate <= range->mMaximum) {
                new_rate = rate;
                break;
            } else if (rate > range->mMaximum) {
                new_rate = range->mMaximum;
            } else {
                new_rate = range->mMinimum;
                break;
            }
        }
        if (new_rate != rate) {
            rate = new_rate;
        }
        free(ranges);
    }
    AudioConverterSetProperty(atx_aac_enc_converter_, kAudioConverterEncodeBitRate, sizeof(rate), &rate);

    // set complexity
    uint32_t quality;
    if (!atx_aac_enc_converter_) {
        return -1;
    }

    complexity *= 10;

    if (complexity == 0) {
        quality = kAudioConverterQuality_Min;
    } else if (complexity <= 25) {
        quality = kAudioConverterQuality_Low;
    } else if (complexity <= 50) {
        quality = kAudioConverterQuality_Medium;
    } else if (complexity <= 75) {
        quality = kAudioConverterQuality_High;
    } else {
        quality = kAudioConverterQuality_Max;
    }

    status = AudioConverterSetProperty(atx_aac_enc_converter_, kAudioConverterCodecQuality, sizeof(quality), &quality);
    if (status != 0) {
        return -1;
    }

    AudioChannelLayout layout;
    layout.mChannelLayoutTag =
            (channels == 1) ? kAudioChannelLayoutTag_Mono : kAudioChannelLayoutTag_Stereo;
    layout.mNumberChannelDescriptions = 0;
    array_size = sizeof(AudioChannelLayout);
    if (AudioConverterSetProperty(atx_aac_enc_converter_, kAudioConverterOutputChannelLayout, array_size, &layout)) {
        return -1;
    }

    // 5. get input frame length ...
    array_size = sizeof(uint32_t);
    AudioConverterGetProperty(atx_aac_enc_converter_, kAudioConverterPropertyMaximumOutputPacketSize, &array_size,
                              &max_encoded_bytes_);

    if (max_encoded_bytes_ <= 0)
        max_encoded_bytes_ = 1024 * 50;

    array_size = sizeof(out_format);
    status = AudioConverterGetProperty(atx_aac_enc_converter_, kAudioConverterCurrentOutputStreamDescription, &array_size,
                                       &out_format);

    if (out_format.mFramesPerPacket && !status) {
        enc_frame_samples = out_format.mFramesPerPacket;
    } else {
        return -1;
    }

    printf("aac input length: %d, max encoded bytes: %d\n", enc_frame_samples, max_encoded_bytes_);

    int32_t fill_buffer_size =
            enc_frame_samples * channels * 4 * sizeof(int16_t);
    fill_buffer_.reset(new RXAudioFillBuffer(fill_buffer_size));
    fill_buffer_->clear();

    // write bitstream
    fbitstream = fopen(bitstreamfile, "wb");
    if (!fbitstream) {
        perror(bitstreamfile);
        return 1;
    }

    framesize_ms = (double)1000.0*enc_frame_samples/sample_rate;
    input_size = channels*2*enc_frame_samples;
    input_buf = (uint8_t*) malloc(input_size);
    convert_buf = (int16_t*) malloc(input_size);
    int count = 0;

    while (1) {
        OSStatus ret;
        void* out_ptr;
        UInt32 numberFrames;

        int in_size, in_elem_size;
        int out_size, out_elem_size;
        int read, i;
        uint8_t outbuf[20480];

        // fill_buffer_->clear();
        read = wav_read_data(wav, input_buf, input_size);

        if (read <= 0) {
            break;
        }

        for (i = 0; i < read/2; i++) {
            const uint8_t* in = &input_buf[2*i];
            convert_buf[i] = in[0] | (in[1] << 8);
        }
        // save input audio frame as packet's frame length
        fill_buffer_->write_data(input_buf, input_size);
        if (static_cast<unsigned long>(fill_buffer_->avaiable_read_len()) < channels * enc_frame_samples * sizeof(int16_t)) {
            continue;
        }
        
        out_ptr = outbuf;
        // out_size = sizeof(outbuf);
        // out_elem_size = 1;
        // out_buf.numBufs = 1;
        // out_buf.bufs = &out_ptr;
        // out_buf.bufferIdentifiers = &out_identifier;
        // out_buf.bufSizes = &out_size;
        // out_buf.bufElSizes = &out_elem_size;
        count++;

        int64_t start_time = current_system_clock();

        // if ((err = AudioX_aacEncEncode(handle, &in_buf, &out_buf, &in_args, &out_args)) != AACENC_OK) {
        //     if (err == AACENC_ENCODE_EOF)
        //         break;
        //     fprintf(stderr, "Encoding failed\n");
        //     return 1;
        // }

        // do atx-aac encode
        AudioBufferList out_buffers;
        out_buffers.mNumberBuffers = 1;
        out_buffers.mBuffers[0].mNumberChannels = channels;
        out_buffers.mBuffers[0].mDataByteSize = max_encoded_bytes_;
        out_buffers.mBuffers[0].mData = out_ptr;
        AudioStreamPacketDescription out_pkt_desc;
        memset(&out_pkt_desc, 0, sizeof(AudioStreamPacketDescription));
        std::unique_ptr<UserData> ud(new UserData());
        ud->channels = channels;
        ud->frame_size = enc_frame_samples;
        ud->fill_buffer = fill_buffer_.get();
        numberFrames = 1;
        ret = AudioConverterFillComplexBuffer(atx_aac_enc_converter_, IoProc, ud.get(), &numberFrames,
                                          &out_buffers, &out_pkt_desc);
        if ((!ret || kNoMoreData == ret) && numberFrames) {
            // is ok
        } else if (ret && ret != kNoMoreData) {
            printf("atx-aac encode error, error code:%d\n ", ret);
            return -1;
        } else {
            // enter case when ret == KNoMoreData && 0== numberFrames, maybe enter for first frame
            continue;
        }

        int64_t end_time = current_system_clock();
        int64_t enc_one_frame_time_ns = end_time - start_time;
        enc_total_rtf_ns += enc_one_frame_time_ns;
        if (enc_one_frame_time_ns > enc_max_rtf){
            enc_max_rtf = (double)enc_one_frame_time_ns;
        }

        // if (out_args.numOutBytes == 0) {
        //     printf("delay %d samples.\n", in_args.numInSamples);
        //     continue;
        // }
        unsigned char int_field[4];
        int_to_char(out_pkt_desc.mDataByteSize, int_field);
        if (fwrite(int_field, 1, 4, fbitstream) != 4) {
            fprintf(stderr, "Error writing.\n");
        }
        fwrite(outbuf, 1, out_pkt_desc.mDataByteSize, fbitstream);
        printf("Encode count %d.\n", count);
        printf("numOutBytes = %d, frameLength = %d.\n", out_pkt_desc.mDataByteSize, enc_frame_samples);
    }
    AudioConverterDispose(atx_aac_enc_converter_);
    printf("%d frames encoded.\n", count);
    free(input_buf);
    free(convert_buf);
    fclose(fbitstream);
    wav_read_close(wav);

    printf("---- Start decoding. ----\n");
    // Decode part
    count = 0;
    std::unique_ptr<uint8_t[]> magic_cookies_;
    UInt32 magic_cookies_size_ = 0;
    AudioConverterRef atx_aac_dec_converter_ = nullptr;
    memset(&in_format, 0, sizeof(AudioStreamBasicDescription));
    memset(&out_format, 0, sizeof(AudioStreamBasicDescription));
    magic_cookies_.reset();
    in_format.mFormatID = get_fromat_id(aot);  // kAudioFormatMPEG4AAC;
    in_format.mBytesPerPacket = 0;
    in_format.mSampleRate = sample_rate;
    in_format.mChannelsPerFrame = channels;

    out_format.mFormatID = kAudioFormatLinearPCM;
    out_format.mFormatFlags = kAudioFormatFlagIsSignedInteger | kAudioFormatFlagIsPacked;
    out_format.mFramesPerPacket = 1;
    out_format.mBitsPerChannel = 16;
    out_format.mSampleRate = sample_rate;
    out_format.mChannelsPerFrame = in_format.mChannelsPerFrame;
    out_format.mBytesPerFrame = out_format.mBitsPerChannel / 8 * out_format.mChannelsPerFrame;
    out_format.mBytesPerPacket = out_format.mBytesPerFrame * out_format.mFramesPerPacket;
    out_format.mReserved = 0;

    status = AudioConverterNew(&in_format, &out_format, &atx_aac_dec_converter_);

    if (status) {
        printf("creat atx aac decoder error, error code: %d\n",status);
        return -1;
    }

    // get magic cookies
    {
        AudioConverterRef converter_ref;
        AudioStreamBasicDescription tmp_in_format;
        memset(&tmp_in_format, 0, sizeof(AudioStreamBasicDescription));
        AudioStreamBasicDescription tmp_out_format;
        memset(&tmp_out_format, 0, sizeof(AudioStreamBasicDescription));

        tmp_in_format.mSampleRate = sample_rate;
        tmp_in_format.mFormatID = kAudioFormatLinearPCM;
        tmp_in_format.mFormatFlags = kAudioFormatFlagIsSignedInteger | kAudioFormatFlagIsPacked;
        tmp_in_format.mBytesPerPacket = sizeof(int16_t) * channels;
        tmp_in_format.mFramesPerPacket = 1;
        tmp_in_format.mBytesPerFrame = sizeof(int16_t) * channels;
        tmp_in_format.mChannelsPerFrame = channels;
        tmp_in_format.mBitsPerChannel = 16;

        tmp_out_format.mSampleRate = sample_rate;
        tmp_out_format.mFormatID = get_fromat_id(aot);
        tmp_out_format.mChannelsPerFrame = tmp_in_format.mChannelsPerFrame;

        status = AudioConverterNew(&tmp_in_format, &tmp_out_format, &converter_ref);
        if (status) {
            return -1;
        }

        status = AudioConverterGetPropertyInfo(converter_ref, kAudioConverterCompressionMagicCookie, &magic_cookies_size_,
                                            NULL);
        if (status || !magic_cookies_size_) {
            AudioConverterDispose(converter_ref);
            return -1;
        }

        magic_cookies_.reset(new uint8_t[magic_cookies_size_]);
        status = AudioConverterGetProperty(converter_ref, kAudioConverterCompressionMagicCookie, &magic_cookies_size_,
                                        magic_cookies_.get());
        if (status) {
            AudioConverterDispose(converter_ref);
            return -1;
        }

        AudioConverterDispose(converter_ref);
    }

    status = AudioConverterSetProperty(atx_aac_dec_converter_, kAudioConverterDecompressionMagicCookie,
                                        magic_cookies_size_, magic_cookies_.get());
    if (status) {
        printf("atx decoder set magic cookie error: %d\n", status);
        // set cookie error, re-creat aaclc decoder
        AudioConverterDispose(atx_aac_dec_converter_);
        in_format.mFormatID = kAudioFormatMPEG4AAC;
        AudioConverterNew(&in_format, &out_format, &atx_aac_dec_converter_);
    }

    if (!atx_aac_dec_converter_) {
        printf("creat atx aac decoder failed\n");
        return -1;
    }

    size = sizeof(format);
    AudioConverterGetProperty(atx_aac_dec_converter_, kAudioConverterCurrentInputStreamDescription, &size, &format);


//     int length = 4;
//     uint8_t* asc = (uint8_t*)malloc(length);
//     struct DecoderType dec_type;
//     dec_type.aac_aot = aot;
//     dec_type.sample_rate = sample_rate;
//     dec_type.channels_num = channels;
//     AAC_DECODER_ERROR err = AAC_DEC_OK;

//     HANDLE_AACDECODER fdkaac_dec_handle_ = NULL;
//     fdkaac_dec_handle_ = AudioX_aacDecoder_Open(TT_MP4_RAW, 1);
//     if ((err = AudioX_aacDecoder_SetParam(fdkaac_dec_handle_, AAC_PCM_MAX_OUTPUT_CHANNELS, dec_type.channels_num)) != AAC_DEC_OK) {
//         printf("Unable to set fdkaac maxoutput channel.\n");
//     }

//     // set plc mode
//     if ((err = AudioX_aacDecoder_SetParam(fdkaac_dec_handle_, AAC_CONCEAL_METHOD, 2)) != AAC_DEC_OK) {
//         printf("Unable to set fdkaac concealment method.\n");
//     }

//     unsigned int invalid_len = rx_audio_gen_aac_asc(dec_type, asc, length);
//     if (invalid_len > 0) {
//         err = AudioX_aacDecoder_ConfigRaw(fdkaac_dec_handle_, &asc, &invalid_len);
//         if (AAC_DEC_OK != err) {
//             AudioX_aacDecoder_Close(fdkaac_dec_handle_);
//             fdkaac_dec_handle_ = NULL;
//             printf("aacDecoder_ConfigRaw failed, error code: %d.\n", err);
//         }
//     }
//     CStreamInfo* decode_info = AudioX_aacDecoder_GetStreamInfo(fdkaac_dec_handle_);

    fbitstream = fopen(bitstreamfile, "rb");
    uint32_t encoded_len;
    int32_t num_read;
    uint8_t dec_bitstream_buf[65535];
    unsigned char *fbytes=NULL;
    fbytes = (unsigned char*)malloc(65536);

    // FILE* fpcm = fopen("test.pcm", "wb");

//     unsigned char *fdk_aac_input_buffer;
//     fdk_aac_input_buffer = (unsigned char*)malloc(65536);

//     int32_t cnt = 0;
//     uint32_t num_read, encoded_len;
//     short *outdata=NULL;
//     outdata = (short*)malloc(65536 * sizeof(short));
//     unsigned char *fbytes=NULL;
//     fbytes = (unsigned char*)malloc(65536);
    while (1) {
        count++;
        unsigned char ch[4];
        num_read = fread(ch, 1, 4, fbitstream);
        if (num_read!=4)
            break;
        encoded_len = char_to_int(ch);
        num_read = fread(dec_bitstream_buf, 1, encoded_len, fbitstream);
        if (num_read!=encoded_len) {
            printf("Read data error.\n");
            break;
        }
        std::unique_ptr<RXAudioPcmBuffer> decoded(new RXAudioPcmBuffer());
        uint32_t inital_size = decoded->get_actual_size();
    
        AudioBufferList dec_out_buffers;
        memset(&dec_out_buffers, 0, sizeof(AudioBufferList));
        DecUserData usr_date;
        UInt32 frame_size = MAX_AUDIO_FRAMES;
        AudioStreamPacketDescription dec_out_pkt_desc;
        memset(&dec_out_pkt_desc, 0, sizeof(AudioStreamPacketDescription));
        uint32_t decdeded_size;

        usr_date.data = reinterpret_cast<void*>(dec_bitstream_buf);
        usr_date.data_bytes = encoded_len;
        usr_date.channels = channels;

        do {
            dec_out_buffers.mNumberBuffers = 1;
            dec_out_buffers.mBuffers[0].mNumberChannels = channels;
            dec_out_buffers.mBuffers[0].mDataByteSize = channels * MAX_AUDIO_FRAMES * sizeof(int16_t);
            dec_out_buffers.mBuffers[0].mData = reinterpret_cast<void*>(decoded->data_buffer_w());

            dec_out_pkt_desc.mDataByteSize = MAX_AUDIO_FRAMES * channels;
            dec_out_pkt_desc.mStartOffset = 0;
            dec_out_pkt_desc.mVariableFramesInPacket = 0;

            status = AudioConverterFillComplexBuffer(atx_aac_dec_converter_, dec_io_proc_callback, &usr_date, &frame_size,
                                                    &dec_out_buffers, &dec_out_pkt_desc);
            // output_timestamp_ = atx_aac_converter_->get_output_timestamp();
            if (status) {
                // RX_LOG(kRXLSError) << "!!!!atx aac decode error, error code: " << status;
                break;
            }

            decoded->set_rate_and_channels(sample_rate, channels);
            decoded->add_actual_size(channels * MAX_AUDIO_FRAMES);
        } while (true);
        decoded->set_mute(false);
// //	   printf("count = %d, encoded_len = %d.\n", count, encoded_len);
//         num_read = fread(fdk_aac_input_buffer, 1, encoded_len, fbitstream);
//         if (num_read!=encoded_len)
//             printf("Read data error.\n");
//         uint32_t bytes_valid = encoded_len;

//         err = AudioX_aacDecoder_Fill(fdkaac_dec_handle_, &fdk_aac_input_buffer, &encoded_len, &bytes_valid);
//         if (AAC_DEC_OK != err) {
//             AudioX_aacDecoder_Close(fdkaac_dec_handle_);
//             fdkaac_dec_handle_ = NULL;
//             printf("aacDecoder filldata failed, error code: %d.\n", err);
//         }


//         int64_t start_time = current_system_clock();
//         err = AudioX_aacDecoder_DecodeFrame(fdkaac_dec_handle_, outdata, 65536, 0);
//         int64_t end_time = current_system_clock();
//         int64_t dec_one_frame_time_ns = end_time - start_time;
//         dec_total_rtf_ns += dec_one_frame_time_ns;
//         if (dec_one_frame_time_ns > dec_max_rtf){
//             dec_max_rtf = (double)dec_one_frame_time_ns;
//         }

//         if (AAC_DEC_OK != err) {
//             AudioX_aacDecoder_Close(fdkaac_dec_handle_);
//             fdkaac_dec_handle_ = NULL;
//             printf("aacDecoder DecodeFrame failed, error code: %d.\n", err);
//         }
//         decode_info = AudioX_aacDecoder_GetStreamInfo(fdkaac_dec_handle_);
//         int decode_samples = decode_info->frameSize;
// //       printf("frame = %d, bitstream_len = %d.\n", count, encoded_len);

        // write data to out file
        for(int i=0;i<decoded->get_actual_size();i++)
        {
            short s;
            s=decoded->data_buffer()[i];
            fbytes[2*i]=s&0xFF;
            fbytes[2*i+1]=(s>>8)&0xFF;
        }

        wav_write_data(fout_wav,fbytes, sizeof(short)*decoded->get_actual_size());
        // fwrite(decoded->data_buffer(), 2, decoded->get_actual_size(), fpcm);
    }

    //print rtf infor.
    average_enc_rtf_ns = (enc_total_rtf_ns/(1000000*framesize_ms)) / count;
    //fprintf (stderr, "average enc rtf(ms/ms):             %7.5f\n",average_enc_rtf);
    fprintf (stderr, "average enc rtf(ms/ms):             %7.5f\n",average_enc_rtf_ns);
    average_dec_rtf_ns = ((dec_total_rtf_ns)/(1000000*framesize_ms)) / count;
    fprintf (stderr, "average dec rtf(ms/ms):             %7.5f\n",average_dec_rtf_ns);

    double max_enc_rtf = (enc_max_rtf/(1000000*framesize_ms)) ;
    fprintf (stderr, "max enc rtf(ms/ms):                 %7.5f\n",max_enc_rtf);
    double max_dec_rtf = (dec_max_rtf/(1000000*framesize_ms)) ;
    fprintf (stderr, "max dec rtf(ms/ms):                 %7.5f\n",max_dec_rtf);


    // free(fdk_aac_input_buffer);
    // free(outdata);
    // free(fbytes);
    if (fbitstream)
        fclose(fbitstream);

    if (fout_wav)
        wav_write_close(fout_wav);

    // AudioX_aacDecoder_Close(fdkaac_dec_handle_);

    return 0;
}

