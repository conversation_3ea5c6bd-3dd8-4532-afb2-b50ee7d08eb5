#include "aacenc_lib.h"
#include "test_utils.h"
#include "encoder_impl.h"
#include "gtest/gtest.h"
#include "../../wavreader.h"
#include <filesystem>

using namespace std;
namespace fs = std::filesystem;

namespace test_encode_transcode {

class TestCreateAACEncoder
    : public ::testing::TestWithParam<std::tuple<
                        AUDIO_OBJECT_TYPE, // aot(AOT_AAC_LC, AOT_SBR, AOT_PS), 0
                        TRANSPORT_TYPE,    // transtype(TT_MP4_RAW, TT_MP4_ADIF, TT_MP4_ADTS), 1
                        int,               // samplerate(44100, 48000), 2
                        int,               // channels(1, 2), 3
                        int,               // afterburner(0, 1), 4
                        int,               // eld_sbr(0, 1), 5
                        int,               // eld_v2(0, 1), 6
                        int,               // signaling(-1, 0, 1, 2), 7
                        int,               // latm(0, 1), 8
                        int,               // header_period(0~0xffff), 9
                        int,               // vbr(0, 1, 2, 3, 4, 5), 10
//                        int,               // bitrate_mode(0, 1, 2, 3), 11
                        int               // bitrate, 12
//                        int                // abr(0, 1) 13
                        >> {
protected:
    void SetUp() override {
        auto test_params = GetParam();
        test_encode_context_.aot = std::get<0>(test_params);
        test_encode_context_.transtype = std::get<1>(test_params);
        test_encode_context_.samplerate = std::get<2>(test_params);
        test_encode_context_.channels = std::get<3>(test_params);
        test_encode_context_.afterburner = std::get<4>(test_params);
        test_encode_context_.eld_sbr = std::get<5>(test_params);
        test_encode_context_.eld_v2 = std::get<6>(test_params);
        test_encode_context_.signaling = std::get<7>(test_params);
        test_encode_context_.latm = std::get<8>(test_params);
        test_encode_context_.header_period = std::get<9>(test_params);
        test_encode_context_.vbr = std::get<10>(test_params);
//        test_encode_context_.bitrate_mode = std::get<11>(test_params);
        test_encode_context_.bitrate = std::get<11>(test_params);
//        test_encode_context_.abr = std::get<13>(test_params);
        if (test_encode_context_.aot == AOT_PS && test_encode_context_.channels == 1) {
            test_encode_context_.aot = AOT_SBR;
        }
    }
    EncoderContext test_encode_context_;
};

TEST_P(TestCreateAACEncoder, test_create_aac_encoder_with_params) {
    AACENC_ERROR err = AACENC_OK;
    err = AudioX_aac_encode_init(&test_encode_context_);
    EXPECT_EQ(err, AACENC_OK);
    sleep_ms(1);
    AudioX_aac_encode_close(&test_encode_context_);
}

class TestAACEncoderProcessRandData
    : public  TestCreateAACEncoder {
};

TEST_P(TestAACEncoderProcessRandData, test_aac_encode_random_data) {

    int max_process_cnt = 50;
    int max_flush_times = 10;
    int i = 0;
    AACENC_ERROR err = AACENC_OK;
    err = AudioX_aac_encode_init(&test_encode_context_);
    EXPECT_EQ(err, AACENC_OK);

    AudioFrame input_frame;
    input_frame.samples_per_channel = test_encode_context_.frame_size;
    input_frame.channels = test_encode_context_.channels;
    AudioStream output_stream;
    for (i = 0; i < max_process_cnt; i++) {
        insert_random_data((uint8_t*)(input_frame.pcm_data), 2*input_frame.samples_per_channel*input_frame.channels);
        err = AudioX_aac_encode_frame(&test_encode_context_, &input_frame, &output_stream);
        EXPECT_EQ(err, AACENC_OK);
        EXPECT_GT(output_stream.encoded_len, 0);
    }

    for (i = 0; i < max_flush_times; i++) {
        err = AudioX_aac_encode_frame(&test_encode_context_, nullptr, &output_stream);
        EXPECT_EQ(err, AACENC_OK);
    }

    AudioX_aac_encode_close(&test_encode_context_);
}

class TestAACEncoderProcessWaveFile
    : public  TestCreateAACEncoder {
public:
    AACENC_ERROR encode_wav_to_aac_adts(std::string input_wav_path) {

        AACENC_ERROR err = AACENC_OK;
        void *wav = nullptr;
        int sample_rate = 44100, channels = 1, bits_per_sample = 16;
        int process_cnt = 0;
        int max_process_cnt = 500;
        int format = 0;
        int read = -1;
        int max_flush_times = 10;
        int i = 0;
        FILE *fbitstream = nullptr;
        std::string output_dir = get_resource_gen_dir("aac");
        std::string output_file;
        AudioFrame input_frame;
        AudioStream output_stream;

        if (test_encode_context_.vbr > 0 && test_encode_context_.bitrate_mode > 0) {
            fprintf(stderr, "unsupported params combination, vbr:%d, bitrate_mode:%d\n", test_encode_context_.vbr, test_encode_context_.bitrate_mode);
            goto encode_failure;
        }

        wav = wav_read_open(input_wav_path.c_str());
        if (!wav) {
            fprintf(stderr, "Unable to open wav file %s\n", input_wav_path.c_str());
            err = AACENC_ENCODE_ERROR;
            goto encode_failure;
        }
        if (!wav_get_header(wav, &format, &channels, &sample_rate, &bits_per_sample, NULL)) {
            fprintf(stderr, "Bad wav file %s\n", input_wav_path.c_str());
            err = AACENC_ENCODE_ERROR;
            goto encode_failure;
        }
        test_encode_context_.samplerate = sample_rate;
        test_encode_context_.channels = channels;

        // init encoder
        err = AudioX_aac_encode_init(&test_encode_context_);
        EXPECT_EQ(err, AACENC_OK);
        if (err != AACENC_OK) {
            goto encode_failure;
        }

        input_frame.channels = test_encode_context_.channels;
        if (format != 1) {
            fprintf(stderr, "Unsupported WAV format %d\n", format);
            err = AACENC_ENCODE_ERROR;
            goto encode_failure;
        }
        if (bits_per_sample != 16) {
            fprintf(stderr, "Unsupported WAV sample depth %d\n", bits_per_sample);
            err = AACENC_ENCODE_ERROR;
            goto encode_failure;
        }
        if(output_dir.empty()) {
            fprintf(stderr, "can't open gen dir.\n");
            err = AACENC_ENCODE_ERROR;
            goto encode_failure;
        }
        output_file = output_dir + "/" + fs::path(input_wav_path).stem().string() +
                        "_aot" + std::to_string(test_encode_context_.aot) + 
                        "_bitrate" + std::to_string(test_encode_context_.bitrate) +
                        "_vbr" + std::to_string(test_encode_context_.vbr) +
                        "_bitratemode" +std::to_string(test_encode_context_.bitrate_mode)
                        + ".aac";
        fbitstream = fopen(output_file.c_str(), "wb");
        if (!fbitstream) {
            fprintf(stderr, "can't open bitstream file: %s.\n", output_file.c_str());
            err = AACENC_ENCODE_ERROR;
            goto encode_failure;
        }

        while (1) {
            read = wav_read_data(wav, (uint8_t*)(input_frame.pcm_data), 2*test_encode_context_.frame_size*input_frame.channels);
            if (read <= 0) {
                break;
            }
            input_frame.samples_per_channel = read/(2*input_frame.channels);
            err = AudioX_aac_encode_frame(&test_encode_context_, &input_frame, &output_stream);
            EXPECT_EQ(err, AACENC_OK);
            fwrite(output_stream.encoded_data, 1, output_stream.encoded_len, fbitstream);
            process_cnt += 1;
        }

        for (i = 0; i < max_flush_times; i++) {
            err = AudioX_aac_encode_frame(&test_encode_context_, nullptr, &output_stream);
            EXPECT_EQ(err, AACENC_OK);
            fwrite(output_stream.encoded_data, 1, output_stream.encoded_len, fbitstream);
        }

encode_failure:
        if (wav) {      
            wav_read_close(wav);
        }
        if (fbitstream) {
            fclose(fbitstream);
        }
        AudioX_aac_encode_close(&test_encode_context_);
        return err;
    }
};

TEST_P(TestAACEncoderProcessWaveFile, test_aac_encode_wav_data) {
    AACENC_ERROR err = AACENC_OK;
    std::string resource_dir = get_resource_dir("wav/test_wav");
    std::vector<std::string> wav_list = list_all_files_with_extension(resource_dir, ".wav");
    if (wav_list.empty()) {
        fprintf(stderr, "No files in (%s).\n", resource_dir.c_str());
        FAIL();
    }
    auto wav_file_path = random_pick(wav_list);
    err = encode_wav_to_aac_adts(wav_file_path);
    EXPECT_EQ(err, AACENC_OK);
}

using ::testing::Combine;
using ::testing::Values;
using ::testing::Range;

#if !defined(BUILD_ALL_UT)
INSTANTIATE_TEST_SUITE_P(TestCreateAACEncoder_1, TestCreateAACEncoder,
                        Combine(Values(AOT_AAC_LC, AOT_SBR, AOT_PS),           // aot
                                Values(TT_MP4_RAW, TT_MP4_ADIF, TT_MP4_ADTS),  // transtype
                                Values(44100, 48000),                          // samplerate
                                Values(1, 2),                                  // channels
                                Values(1),                                     // afterburner
                                Values(0),                                     // eld_sbr
                                Values(0),                                     // eld_v2
                                Values(-1),                                    // signaling
                                Values(0),                                     // latm
                                Values(0),                                     // header_period
                                Values(0, 1, 2, 3, 4, 5),                      // vbr
//                                Values(0, 1, 2, 3),                            // bitrate_mode
                                Values(48000, 50000, 54000, 64000, 128000)    // bitrate
//                                Values(0, 1)                                   // abr
                            ));

INSTANTIATE_TEST_SUITE_P(TestAACEncoderProcessRandData_1, TestAACEncoderProcessRandData,
                        Combine(Values(AOT_AAC_LC, AOT_SBR, AOT_PS),           // aot
                                Values(TT_MP4_RAW, TT_MP4_ADIF, TT_MP4_ADTS),  // transtype
                                Values(44100, 48000),                          // samplerate
                                Values(1, 2),                                  // channels
                                Values(1),                                     // afterburner
                                Values(0),                                     // eld_sbr
                                Values(0),                                     // eld_v2
                                Values(-1),                                    // signaling
                                Values(0),                                     // latm
                                Values(0),                                     // header_period
                                Values(0, 1, 2, 3, 4, 5),                      // vbr
//                                Values(0, 1, 2, 3),                            // bitrate_mode
                                Values(48000, 50000, 54000, 64000, 128000)    // bitrate
//                                Values(0, 1)                                   // abr
                            ));

INSTANTIATE_TEST_SUITE_P(TestAACEncoderProcessWaveFile_1, TestAACEncoderProcessWaveFile,
                        Combine(Values(AOT_AAC_LC, AOT_SBR, AOT_PS),           // aot
                                Values(TT_MP4_ADTS),                           // transtype
                                Values(44100),                                 // samplerate
                                Values(1),                                     // channels
                                Values(1),                                     // afterburner
                                Values(0),                                     // eld_sbr
                                Values(0),                                     // eld_v2
                                Values(-1),                                    // signaling
                                Values(0),                                     // latm
                                Values(0),                                     // header_period
                                Values(0, 1, 2, 3, 4, 5),                      // vbr
//                                Values(0, 1, 2, 3),                            // bitrate_mode
                                Values(48000, 50000, 54000, 64000, 128000)   // bitrate
//                                Values(0, 1)                                   // abr
                            ));
#else
/* Test params usually used.*/
INSTANTIATE_TEST_SUITE_P(TestCreateAACEncoder_1, TestCreateAACEncoder,
                        Combine(Values(AOT_AAC_LC, AOT_SBR, AOT_PS),           // aot
                                Values(TT_MP4_RAW, TT_MP4_ADIF, TT_MP4_ADTS),  // transtype
                                Values(44100, 48000),                          // samplerate
                                Values(1, 2),                                  // channels
                                Values(0, 1),                                  // afterburner
                                Values(0),                                     // eld_sbr
                                Values(0),                                     // eld_v2
                                Values(-1),                                    // signaling
                                Values(0),                                     // latm
                                Values(0),                                     // header_period
                                Values(0, 1, 2, 3, 4, 5),                      // vbr
//                                Values(0, 1, 2, 3),                            // bitrate_mode
                                Values(48000, 64000, 128000)                  // bitrate
//                                Values(0, 1)                                   // abr
                            ));
/* Test abr params.*/
INSTANTIATE_TEST_SUITE_P(TestCreateAACEncoder_2, TestCreateAACEncoder,
                        Combine(Values(AOT_AAC_LC, AOT_SBR, AOT_PS),           // aot
                                Values(TT_MP4_RAW, TT_MP4_ADIF, TT_MP4_ADTS),  // transtype
                                Values(44100, 48000),                          // samplerate
                                Values(1, 2),                                  // channels
                                Values(0, 1),                                  // afterburner
                                Values(0),                                     // eld_sbr
                                Values(0),                                     // eld_v2
                                Values(-1),                                    // signaling
                                Values(0),                                     // latm
                                Values(0),                                     // header_period
                                Values(0, 1, 2, 3, 4, 5),                      // vbr
//                                Values(0, 1, 2, 3),                            // bitrate_mode
                                Range(48000, 64000, 2000)                     // bitrate
//                                Values(0, 1)                                   // abr
                            ));
/* Test params usually used.*/
INSTANTIATE_TEST_SUITE_P(TestAACEncoderProcessRandData_1, TestAACEncoderProcessRandData,
                        Combine(Values(AOT_AAC_LC, AOT_SBR, AOT_PS),           // aot
                                Values(TT_MP4_RAW, TT_MP4_ADIF, TT_MP4_ADTS),  // transtype
                                Values(44100, 48000),                          // samplerate
                                Values(1, 2),                                  // channels
                                Values(0, 1),                                  // afterburner
                                Values(0),                                     // eld_sbr
                                Values(0),                                     // eld_v2
                                Values(-1),                                    // signaling
                                Values(0),                                     // latm
                                Values(0),                                     // header_period
                                Values(0, 1, 2, 3, 4, 5),                      // vbr
//                                Values(0, 1, 2, 3),                            // bitrate_mode
                                Values(48000, 64000, 128000)                  // bitrate
//                                Values(0, 1)                                   // abr
                            ));
/* Test abr params.*/
INSTANTIATE_TEST_SUITE_P(TestAACEncoderProcessRandData_2, TestAACEncoderProcessRandData,
                        Combine(Values(AOT_AAC_LC, AOT_SBR, AOT_PS),           // aot
                                Values(TT_MP4_RAW, TT_MP4_ADIF, TT_MP4_ADTS),  // transtype
                                Values(44100, 48000),                          // samplerate
                                Values(1, 2),                                  // channels
                                Values(0, 1),                                  // afterburner
                                Values(0),                                     // eld_sbr
                                Values(0),                                     // eld_v2
                                Values(-1),                                    // signaling
                                Values(0),                                     // latm
                                Values(0),                                     // header_period
                                Values(0, 1, 2, 3, 4, 5),                      // vbr
//                                Values(0, 1, 2, 3),                            // bitrate_mode
                                Range(48000, 64000, 2000)                    // bitrate
//                                Values(0, 1)                                   // abr
                            ));
/* Test params usually used.*/
INSTANTIATE_TEST_SUITE_P(TestAACEncoderProcessWaveFile_1, TestAACEncoderProcessWaveFile,
                        Combine(Values(AOT_AAC_LC, AOT_SBR, AOT_PS),           // aot
                                Values(TT_MP4_ADTS),                           // transtype
                                Values(44100),                                 // samplerate
                                Values(1),                                     // channels
                                Values(0, 1),                                  // afterburner
                                Values(0),                                     // eld_sbr
                                Values(0),                                     // eld_v2
                                Values(-1),                                    // signaling
                                Values(0),                                     // latm
                                Values(0),                                     // header_period
                                Values(0, 1, 2, 3, 4, 5),                      // vbr
//                                Values(0, 1, 2, 3),                            // bitrate_mode
                                Values(48000, 64000, 128000)                  // bitrate
//                                Values(0, 1)                                   // abr
                            ));
/* Test abr params.*/
INSTANTIATE_TEST_SUITE_P(TestAACEncoderProcessWaveFile_2, TestAACEncoderProcessWaveFile,
                        Combine(Values(AOT_AAC_LC, AOT_SBR, AOT_PS),           // aot
                                Values(TT_MP4_ADTS),                           // transtype
                                Values(44100),                                 // samplerate
                                Values(1),                                     // channels
                                Values(0, 1),                                  // afterburner
                                Values(0),                                     // eld_sbr
                                Values(0),                                     // eld_v2
                                Values(-1),                                    // signaling
                                Values(0),                                     // latm
                                Values(0),                                     // header_period
                                Values(0, 1, 2, 3, 4, 5),                      // vbr
//                                Values(0, 1, 2, 3),                            // bitrate_mode
                                Range(48000, 64000, 2000)                     // bitrate
//                                Values(0, 1)                                   // abr
                            ));
#endif

std::tuple<AUDIO_OBJECT_TYPE, // aot(AOT_AAC_LC, AOT_SBR, AOT_PS), 0
            TRANSPORT_TYPE,    // transtype(TT_MP4_RAW, TT_MP4_ADIF, TT_MP4_ADTS), 1
            int,               // samplerate(44100, 48000), 2
            int,               // channels(1, 2), 3
            int,               // afterburner(0, 1), 4
            int,               // eld_sbr(0, 1), 5
            int,               // eld_v2(0, 1), 6
            int,               // signaling(-1, 0, 1, 2), 7
            int,               // latm(0, 1), 8
            int,               // header_period(0~0xffff), 9
            int,               // vbr(0, 1, 2, 3, 4, 5), 10
//            int,               // bitrate_mode(0, 1, 2, 3), 11
            int               // bitrate, 11
//            int                // abr(0, 1) 13
        > const test_specific_case_table[] = {
    std::make_tuple(AOT_AAC_LC, TT_MP4_ADTS, 44100, 1, 1, 0, 0, -1, 0, 0, 0, 48000),
};

INSTANTIATE_TEST_SUITE_P(TestAACEncoderSpecificCase, TestAACEncoderProcessWaveFile,
                        ::testing::ValuesIn(test_specific_case_table));
}  // namespace test_encode