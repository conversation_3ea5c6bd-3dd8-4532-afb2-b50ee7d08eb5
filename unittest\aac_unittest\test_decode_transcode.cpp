#include "test_utils.h"
#include "gtest/gtest.h"
#include "encoder_impl.h"
#include "decoder_impl.h"
#include "../../wavreader.h"
#include "../../wavwriter.h"
#include <filesystem>
#include "push_resampler.h"
#include "memory"

using namespace std;
namespace fs = std::filesystem;

namespace test_decode_transcode {
class TestCreateAACDecoder
    : public ::testing::TestWithParam<std::tuple<
                        int,              // samplerate(44100, 48000), 2
                        int               // channels(1, 2), 3
                        >> {
protected:
    void SetUp() override {
        auto test_params = GetParam();
        test_decode_context_.sample_rate = std::get<0>(test_params);
        test_decode_context_.channels = std::get<1>(test_params);
    }
    DecoderContext test_decode_context_;
};

TEST_P(TestCreateAACDecoder, test_create_aac_decoder_with_params) {
    int err = 0;
    err = AudioX_fdk_aac_decode_init(&test_decode_context_);
    EXPECT_EQ(err, 0);
    sleep_ms(1);
    AudioX_fdk_aac_decode_close(&test_decode_context_);
}

class TestAACEncodeDecode
    : public ::testing::TestWithParam<std::tuple<
                        AUDIO_OBJECT_TYPE, // aot(AOT_AAC_LC, AOT_SBR, AOT_PS), 0
                        int,               // samplerate(44100, 48000), 1
                        int,               // channels(1, 2), 2
                        int,               // afterburner(0, 1), 3
                        int,               // eld_sbr(0, 1), 4
                        int,               // eld_v2(0, 1), 5
                        int,               // signaling(-1, 0, 1, 2), 6
                        int,               // latm(0, 1), 7
                        int,               // header_period(0~0xffff), 8
                        int,               // vbr(0, 1, 2, 3, 4, 5), 9
                        int,               // bitrate_mode(0, 1, 2, 3), 10
                        int,               // bitrate, 11
                        int                // abr(0, 1) 12
                        >> {
public:
    int test_encode_decode(std::string input_wav_path) {
        AACENC_ERROR enc_err = AACENC_OK;
        int err = 0;
        void *wav = nullptr;
        void *fout_wav = nullptr;
        int sample_rate = 44100, channels = 1, bits_per_sample = 16;
        int process_cnt = 0;
        int max_process_cnt = 500;
        int format = 0;
        int read = -1;
        int max_flush_times = 10;
        int i = 0;
        std::string output_aac_dir = get_resource_gen_dir("aac");
        std::string output_wav_dir = get_resource_gen_dir("wav");
        std::string output_aac_file, output_wav_file;
        FILE *fbitstream = nullptr;
        
        AudioFrame input_frame, resample_out_frame, mix_out_frame, decoded_frame, encoder_input_frame;
        AudioStream output_stream;
        std::vector<int16_t> input_buffer_;


        // FILE *fmix_out = nullptr;
        // std::string mix_out_pcm;

        std::unique_ptr<webrtc::PushResampler<int16_t>> test_resample;

        if (test_encode_context_.vbr > 0 && test_encode_context_.bitrate_mode > 0) {
            fprintf(stderr, "unsupported params combination, vbr:%d, bitrate_mode:%d\n", test_encode_context_.vbr, test_encode_context_.bitrate_mode);
            goto test_end;
        }

        wav = wav_read_open(input_wav_path.c_str());
        if (!wav) {
            fprintf(stderr, "Unable to open wav file %s\n", input_wav_path.c_str());
            err = -1;
            goto test_end;
        }
        if (!wav_get_header(wav, &format, &channels, &sample_rate, &bits_per_sample, NULL)) {
            fprintf(stderr, "Bad wav file %s\n", input_wav_path.c_str());
            err = -1;
            goto test_end;
        }

        // init encoder
        enc_err = AudioX_aac_encode_init(&test_encode_context_);
        EXPECT_EQ(enc_err, AACENC_OK);
        if (enc_err != AACENC_OK) {
            err = -1;
            goto test_end;
        }

        // init decoder
        err = AudioX_fdk_aac_decode_init(&test_decode_context_);

        if (format != 1) {
            fprintf(stderr, "Unsupported WAV format %d\n", format);
            err = -1;
            goto test_end;
        }
        if (bits_per_sample != 16) {
            fprintf(stderr, "Unsupported WAV sample depth %d\n", bits_per_sample);
            err = -1;
            goto test_end;
        }
        if(output_aac_dir.empty() || output_wav_dir.empty()) {
            fprintf(stderr, "can't open gen dir.\n");
            err = -1;
            goto test_end;
        }

        output_aac_file = output_aac_dir + "/" + fs::path(input_wav_path).stem().string() +
                        "_samplerate" + std::to_string(test_encode_context_.samplerate) + 
                        "_channel" + std::to_string(test_encode_context_.channels) +
                        "_aot" + std::to_string(test_encode_context_.aot) + 
                        "_bitrate" + std::to_string(test_encode_context_.bitrate) +
                        "_vbr" + std::to_string(test_encode_context_.vbr) +
                        "_bitratemode" +std::to_string(test_encode_context_.bitrate_mode) +
                        "_abr" +std::to_string(test_encode_context_.abr) +
                        + ".aac";
        fbitstream = fopen(output_aac_file.c_str(), "wb");
        // mix_out_pcm = output_dir + "/" + fs::path(input_wav_path).stem().string() +
        //                 "_samplerate" + std::to_string(test_encode_context_.samplerate) + 
        //                 "_channel" + std::to_string(test_encode_context_.channels) +
        //                 "_aot" + std::to_string(test_encode_context_.aot) + 
        //                 "_bitrate" + std::to_string(test_encode_context_.bitrate) +
        //                 "_vbr" + std::to_string(test_encode_context_.vbr) +
        //                 "_bitratemode" +std::to_string(test_encode_context_.bitrate_mode)
        //                 + ".pcm";
        // fmix_out = fopen(mix_out_pcm.c_str(), "wb");

        if (!fbitstream) {
            fprintf(stderr, "can't open bitstream file: %s.\n", output_aac_file.c_str());
            err = -1;
            goto test_end;
        }

        output_wav_file = output_wav_dir + "/" + fs::path(input_wav_path).stem().string() +
                        "_samplerate" + std::to_string(test_encode_context_.samplerate) + 
                        "_channel" + std::to_string(test_encode_context_.channels) +
                        "_aot" + std::to_string(test_encode_context_.aot) + 
                        "_bitrate" + std::to_string(test_encode_context_.bitrate) +
                        "_vbr" + std::to_string(test_encode_context_.vbr) +
                        "_bitratemode" +std::to_string(test_encode_context_.bitrate_mode) +
                        "_abr" +std::to_string(test_encode_context_.abr) +
                        + ".wav";
        fout_wav = wav_write_open(output_wav_file.c_str(), test_decode_context_.sample_rate, 16, test_decode_context_.channels);
        if (!fout_wav) {
            fprintf(stderr, "Unable to open output wav file %s\n", output_wav_file.c_str());
            err = -1;
            goto test_end;
        }

        test_resample = std::make_unique<webrtc::PushResampler<int16_t>>();
        test_resample->InitializeIfNeeded(sample_rate, test_encode_context_.samplerate, channels);

        encoder_input_frame.samples_per_channel = 0;
        encoder_input_frame.channels = test_encode_context_.channels;

        while (1) {
            read = wav_read_data(wav, (uint8_t*)(input_frame.pcm_data), 2*(sample_rate/100)*channels);
            if (read <= 0) {
                break;
            }
            input_frame.channels = channels;
            input_frame.samples_per_channel = read/(2*channels);

            int resample_res = test_resample->Resample(
                input_frame.pcm_data, (sample_rate / 100) * channels,
                resample_out_frame.pcm_data,
                (test_encode_context_.samplerate / 100) * channels);
            
            resample_out_frame.samples_per_channel = resample_res / channels;
            resample_out_frame.channels = channels;

            if (channels == 1 && test_encode_context_.channels == 2) {
                mono_to_stereo(resample_out_frame.pcm_data, resample_out_frame.samples_per_channel, mix_out_frame.pcm_data);
            } else if (channels == 2 && test_encode_context_.channels == 1) {
                stereo_to_mono(resample_out_frame.pcm_data, resample_out_frame.samples_per_channel, mix_out_frame.pcm_data);
            } else {
                memcpy(mix_out_frame.pcm_data, resample_out_frame.pcm_data, sizeof(int16_t)*resample_out_frame.samples_per_channel*resample_out_frame.channels);
            }
            mix_out_frame.channels = test_encode_context_.channels;
            mix_out_frame.samples_per_channel = resample_out_frame.samples_per_channel;

            // fwrite(mix_out_frame.pcm_data, sizeof(int16_t), mix_out_frame.channels*mix_out_frame.samples_per_channel, fmix_out);

            input_buffer_.insert(input_buffer_.end(), mix_out_frame.pcm_data, &mix_out_frame.pcm_data[mix_out_frame.channels*mix_out_frame.samples_per_channel]);

            if ((int)input_buffer_.size() < test_encode_context_.frame_size*test_encode_context_.channels) {
                continue;
            } else {
                memcpy(encoder_input_frame.pcm_data, input_buffer_.data(), sizeof(int16_t)*test_encode_context_.frame_size*test_encode_context_.channels);
                encoder_input_frame.samples_per_channel = test_encode_context_.frame_size;
                encoder_input_frame.channels = test_encode_context_.channels;
                input_buffer_.erase(input_buffer_.begin(), input_buffer_.begin() + test_encode_context_.frame_size*test_encode_context_.channels);
            }
            err = AudioX_aac_encode_frame(&test_encode_context_, &encoder_input_frame, &output_stream);
            EXPECT_EQ(err, AACENC_OK);
            if (output_stream.encoded_len > 0) {
                fwrite(output_stream.encoded_data, 1, output_stream.encoded_len, fbitstream);

                err = AudioX_fdk_aac_decode_frame(&test_decode_context_, &output_stream, &decoded_frame);
                EXPECT_GE(err, 0);
                wav_write_data(fout_wav, (uint8_t*)decoded_frame.pcm_data, 2*decoded_frame.channels*decoded_frame.samples_per_channel);
            }
            
            process_cnt += 1;
        }
test_end:
        if (wav) {      
            wav_read_close(wav);
        }
        if (fout_wav) {
            wav_write_close(fout_wav);
        }
        if (fbitstream) {
            fclose(fbitstream);
        }
        AudioX_aac_encode_close(&test_encode_context_);
        AudioX_fdk_aac_decode_close(&test_decode_context_);
        return err;
    }
protected:
    void SetUp() override {
        auto test_params = GetParam();
        test_encode_context_.aot = std::get<0>(test_params);
        test_encode_context_.transtype = TT_MP4_ADTS;
        test_encode_context_.samplerate = std::get<1>(test_params);
        test_encode_context_.channels = std::get<2>(test_params);
        test_encode_context_.afterburner = std::get<3>(test_params);
        test_encode_context_.eld_sbr = std::get<4>(test_params);
        test_encode_context_.eld_v2 = std::get<5>(test_params);
        test_encode_context_.signaling = std::get<6>(test_params);
        test_encode_context_.latm = std::get<7>(test_params);
        test_encode_context_.header_period = std::get<8>(test_params);
        test_encode_context_.vbr = std::get<9>(test_params);
        test_encode_context_.bitrate_mode = std::get<10>(test_params);
        test_encode_context_.bitrate = std::get<11>(test_params);
        test_encode_context_.abr = std::get<12>(test_params);
        if (test_encode_context_.aot == AOT_PS && test_encode_context_.channels == 1) {
            test_encode_context_.aot = AOT_SBR;
        }
        test_decode_context_.channels = test_encode_context_.channels;
        test_decode_context_.sample_rate = test_encode_context_.samplerate;
    }
    EncoderContext test_encode_context_;
    DecoderContext test_decode_context_;
};

TEST_P(TestAACEncodeDecode, test_encode_decode) {
    int err = 0;
    std::string resource_dir = get_resource_dir("wav/test_wav");
    std::vector<std::string> wav_list = list_all_files_with_extension(resource_dir, ".wav");
    if (wav_list.empty()) {
        fprintf(stderr, "No files in (%s).\n", resource_dir.c_str());
        FAIL();
    }
    auto wav_file_path = random_pick(wav_list);
    err = test_encode_decode(wav_file_path);
}

using ::testing::Combine;
using ::testing::Values;
using ::testing::Range;

#if !defined(BUILD_ALL_UT)
INSTANTIATE_TEST_SUITE_P(TestCreateAACDecoder_1, TestCreateAACDecoder,
                        Combine(Values(44100, 48000),                          // samplerate
                                Values(1, 2)                                   // channels
                            ));

INSTANTIATE_TEST_SUITE_P(TestAACEncodeDecode_1, TestAACEncodeDecode,
                        Combine(Values(AOT_AAC_LC, AOT_SBR, AOT_PS),           // aot
                                Values(44100, 48000),                          // samplerate
                                Values(1, 2),                                  // channels
                                Values(1),                                     // afterburner
                                Values(0),                                     // eld_sbr
                                Values(0),                                     // eld_v2
                                Values(-1),                                    // signaling
                                Values(0),                                     // latm
                                Values(0),                                     // header_period
                                Values(0, 1, 2, 3, 4, 5),                      // vbr
                                Values(0, 1, 2, 3),                            // bitrate_mode
                                Values(48000, 50000, 54000, 64000, 128000),    // bitrate
                                Values(0, 1)                                   // abr
                            ));

#endif

}
