#include "test_utils.h"
#include "chrono"
#include "thread"
#include "cstdlib"
#include "time.h"
#include "random"

#ifdef _WIN32
#include <windows.h>  // Windows API 的头文件
#else
#include <unistd.h>   // POSIX 的头文件
#endif

#include "string"
#include "vector"
#include <filesystem>
#include <cstring>

namespace fs = std::filesystem;

int av_clip_c(int a, int amin, int amax)
{
    if      (a < amin) return amin;
    else if (a > amax) return amax;
    else               return a;
}

bool sleep_ms(int32_t milliseconds) {
    std::chrono::duration<int, std::milli> timespan(milliseconds);
    std::this_thread::sleep_for(timespan);
    return true;
}

void insert_random_data(uint8_t *input_data, int input_size) {
    if (!input_data) {
        return;
    }
    std::random_device rand_seed;
    std::mt19937 gen(rand_seed());
    std::uniform_int_distribution<> dis(0, UINT8_MAX);
    int n = 0;
    for(n = 0; n < input_size; n++) {
        input_data[n] = dis(gen);
    }
}

void insert_zero_data(uint8_t *input_data, int input_size) {
    if (!input_data) {
        return;
    }
    memset(input_data, 0, input_size*sizeof(uint8_t));
}

std::string get_resource_dir(std::string dir_name) {
    std::string resource_path = std::string(FDK_BASE_DIR_PATH) + std::string("/unittest/rtc_resources/xaac_ut_resources");
    if (dir_name != "") {
        resource_path += "/" + dir_name; 
    }
    if (fs::exists(resource_path)) {
        return resource_path;
    } else {
        return "";
    }

}

std::string get_resource_gen_dir(std::string dir_name) {

    std::string resource_gen_path_str = std::string(FDK_BASE_DIR_PATH) + std::string("/unittest/resources/gen");
    if (dir_name != "") {
        resource_gen_path_str += "/" + dir_name; 
    }
    fs::path resource_gen_path(resource_gen_path_str);
    if (!fs::exists(resource_gen_path)) {
        if(fs::create_directories(resource_gen_path)){
            return resource_gen_path.string();
        } else {
		    return "";
	    }
    } else {
        return resource_gen_path.string();
    }
}

std::vector<std::string> list_all_files_with_extension(std::string dir, std::string file_extension) {
    std::vector<std::string> res;
    if (dir.empty() || file_extension.empty()) {
        return {};
    }
    if (!fs::exists(dir)) {
        return {};
    }
    fs::path dir_path(dir); 
    std::filesystem::recursive_directory_iterator file_iter(dir_path);
    for(const auto& iter : file_iter) {
        if (iter.path().extension() == file_extension) {
            res.push_back(iter.path().string());
        }
    }
    return res;
}

void stereo_to_mono(const int16_t* src_audio, int samples_per_channel, int16_t* dst_audio) {
    for (int i = 0; i < samples_per_channel; i++) {
        dst_audio[i] = (static_cast<int32_t>(src_audio[2 * i]) + src_audio[2 * i + 1]) >> 1;
    }
}

void mono_to_stereo(const int16_t* src_audio, int samples_per_channel, int16_t* dst_audio) {
    for (int i = 0; i < samples_per_channel; i++) {
        dst_audio[2*i] = src_audio[i];
        dst_audio[2*i+1] = src_audio[i];
    }
}

