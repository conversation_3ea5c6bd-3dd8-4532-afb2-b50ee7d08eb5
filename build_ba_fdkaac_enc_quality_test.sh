#!/bin/bash
# build fdk-aac encode quality test
set -e
rm -rf output
# output product
if [ ! -d "output" ];then
    mkdir -p output/bin output/lib output/include
fi

#git submodule update --init --recursive third_party/jsoncpp

buildEnv() {
    apt-get install -y sox
    apt-get install -y lame
    apt-get install -y libsox-fmt-all
}

buildPythonEnv() {
    pip3 install --upgrade pip
    pip3 install pandas -i https://bytedpypi.byted.org/simple
}

buildPeaqtool() {
    pushd unittest/peaqtool
    make
    peaq_bin_path=$(readlink -f ./bin/PQevalAudio)
    popd
}

buildAACDemo() {
    rm -rf cmake-build-fdkaac-demo
    mkdir -p cmake-build-fdkaac-demo
    pushd cmake-build-fdkaac-demo
    rm -f CMakeCache.txt
    cmake .. -DBUILD_PROGRAMS=ON \
        -DBUILD_SHARED_LIBS=OFF \
        -DCMAKE_BUILD_TYPE=Release
    make
    aac_demo_bin_path=$(readlink -f ./aac-enc)
#    signal_analysis_bin_path=$(readlink -f ./audio-signal-analysis)
    popd
}

if [ $(uname) == "Linux" ];then
    buildEnv
    buildPythonEnv
fi

buildPeaqtool
buildAACDemo

# copy bin to output dir
cp ${peaq_bin_path} output/bin
cp ${aac_demo_bin_path} output/bin
#cp ./unittest/peaqtool/bin/PQevalAudio output/bin
#cp ./cmake-build-fdkaac-demo/aac-enc output/bin


TestCase1() {
    python3 unittest/aac_unittest/enc_quality_test.py \
        --input_wav_dir unittest/rtc_resources/xaac_ut_resources/wav/test_wav \
        --aac_demo_bin_path output/bin/aac-enc \
        --peaq_bin_path output/bin/PQevalAudio \
        --ref_score_file unittest/rtc_resources/xaac_ut_resources/ref_score.csv \
        --test_aot 29 \
        --test_bitrate 64000 \
        --test_vbr 0
}

TestCase2() {
    python3 unittest/aac_unittest/enc_quality_test.py \
        --input_wav_dir unittest/rtc_resources/xaac_ut_resources/wav/test_wav \
        --aac_demo_bin_path output/bin/aac-enc \
        --peaq_bin_path output/bin/PQevalAudio \
        --ref_score_file unittest/rtc_resources/xaac_ut_resources/ref_score.csv \
        --test_aot 5 \
        --test_bitrate 96000 \
        --test_vbr 0
}

TestCase3() {
    python3 unittest/aac_unittest/enc_quality_test.py \
        --input_wav_dir unittest/rtc_resources/xaac_ut_resources/wav/test_wav \
        --aac_demo_bin_path output/bin/aac-enc \
        --peaq_bin_path output/bin/PQevalAudio \
        --ref_score_file unittest/rtc_resources/xaac_ut_resources/ref_score.csv \
        --test_aot 2 \
        --test_bitrate 128000 \
        --test_vbr 0
}

TestCase4() {
    python3 unittest/aac_unittest/enc_quality_test.py \
        --input_wav_dir unittest/rtc_resources/xaac_ut_resources/wav/test_wav \
        --aac_demo_bin_path output/bin/aac-enc \
        --peaq_bin_path output/bin/PQevalAudio \
        --ref_score_file unittest/rtc_resources/xaac_ut_resources/ref_score.csv \
        --test_aot 2 \
        --test_bitrate 0 \
        --test_vbr 4
}

TestCase5() {
    python3 unittest/aac_unittest/enc_quality_test.py \
        --input_wav_dir unittest/rtc_resources/xaac_ut_resources/wav/test_wav \
        --aac_demo_bin_path output/bin/aac-enc \
        --peaq_bin_path output/bin/PQevalAudio \
        --ref_score_file unittest/rtc_resources/xaac_ut_resources/ref_score.csv \
        --test_aot 5 \
        --test_bitrate 0 \
        --test_vbr 3
}

TestCase6() {
    python3 unittest/aac_unittest/enc_quality_test.py \
        --input_wav_dir unittest/rtc_resources/xaac_ut_resources/wav/test_wav \
        --aac_demo_bin_path output/bin/aac-enc \
        --peaq_bin_path output/bin/PQevalAudio \
        --ref_score_file unittest/rtc_resources/xaac_ut_resources/ref_score.csv \
        --test_aot 29 \
        --test_bitrate 0 \
        --test_vbr 2
}

testSeq=($(seq 1 1 6))

for testCase in ${testSeq[@]}
do
    echo ${testCase}
    TestCase${testCase}
done