/* ------------------------------------------------------------------
 * Copyright (C) 2009 <PERSON>
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either
 * express or implied.
 * See the License for the specific language governing permissions
 * and limitations under the License.
 * -------------------------------------------------------------------
 */

#include "wavwriter.h"
#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <stdint.h>

#define ADTS_HEADER_SIZE 7

struct wav_writer {
	FILE *wav;
	int data_length;

	int sample_rate;
	int bits_per_sample;
	int channels;
};

static void write_string(struct wav_writer* ww, const char *str) {
	fputc(str[0], ww->wav);
	fputc(str[1], ww->wav);
	fputc(str[2], ww->wav);
	fputc(str[3], ww->wav);
}

static void write_int32(struct wav_writer* ww, int value) {
	fputc((value >>  0) & 0xff, ww->wav);
	fputc((value >>  8) & 0xff, ww->wav);
	fputc((value >> 16) & 0xff, ww->wav);
	fputc((value >> 24) & 0xff, ww->wav);
}

static void write_int16(struct wav_writer* ww, int value) {
	fputc((value >> 0) & 0xff, ww->wav);
	fputc((value >> 8) & 0xff, ww->wav);
}

static void write_header(struct wav_writer* ww, int length) {
	int bytes_per_frame, bytes_per_sec;
	write_string(ww, "RIFF");
	write_int32(ww, 4 + 8 + 16 + 8 + length);
	write_string(ww, "WAVE");

	write_string(ww, "fmt ");
	write_int32(ww, 16);

	bytes_per_frame = ww->bits_per_sample/8*ww->channels;
	bytes_per_sec   = bytes_per_frame*ww->sample_rate;
	write_int16(ww, 1);                   // Format
	write_int16(ww, ww->channels);        // Channels
	write_int32(ww, ww->sample_rate);     // Samplerate
	write_int32(ww, bytes_per_sec);       // Bytes per sec
	write_int16(ww, bytes_per_frame);     // Bytes per frame
	write_int16(ww, ww->bits_per_sample); // Bits per sample

	write_string(ww, "data");
	write_int32(ww, length);
}

void* wav_write_open(const char *filename, int sample_rate, int bits_per_sample, int channels) {
	struct wav_writer* ww = (struct wav_writer*) malloc(sizeof(*ww));
	memset(ww, 0, sizeof(*ww));
	ww->wav = fopen(filename, "wb");
	if (ww->wav == NULL) {
		free(ww);
		return NULL;
	}
	ww->data_length = 0;
	ww->sample_rate = sample_rate;
	ww->bits_per_sample = bits_per_sample;
	ww->channels = channels;

	write_header(ww, ww->data_length);
	return ww;
}

void wav_write_close(void* obj) {
	struct wav_writer* ww = (struct wav_writer*) obj;
	if (ww->wav == NULL) {
		free(ww);
		return;
	}
	fseek(ww->wav, 0, SEEK_SET);
	write_header(ww, ww->data_length);
	fclose(ww->wav);
	free(ww);
}

void wav_write_data(void* obj, const unsigned char* data, int length) {
	struct wav_writer* ww = (struct wav_writer*) obj;
	if (ww->wav == NULL)
		return;
	fwrite(data, length, 1, ww->wav);
	ww->data_length += length;
}

static int index_to_samplerate(int index) {
        int samplerate_table[] = {96000, 88200, 64000, 48000, 44100, 32000, 24000, 22050, 16000, 12000, 11025, 8000, 7350, 0, 0, 57600};
        if (index < 0 || index > 15) {
                return 48000;
        } else {
                return samplerate_table[index];
        }
}

int getAACFrame(FILE *fbitstream, unsigned char* buffer, int buf_size, ADTSInfo *adts_info) {
        memset(adts_info, 0, sizeof(ADTSInfo));
        int encoded_len = 0;
        int is_adts = 0;
        int channels = 0;
        if(!fbitstream || !buffer) {
                return -1;
        }
        unsigned char ch[ADTS_HEADER_SIZE];
        size_t num_read = fread(ch, 1, 4, fbitstream);
        if (num_read == 0) {
                // printf("Read data finish.\n");
                return -1;
        }
        if (num_read!=4) {
                printf("Read data error.\n");
                return -1;
        }
        //Sync words
        if((ch[0] == 0xff) && ((ch[1] & 0xf0) == 0xf0) ){ // ch取出来前 12个 bit
                size_t num_read = fread(ch+4, 1, ADTS_HEADER_SIZE-4, fbitstream);
                if (num_read!=(ADTS_HEADER_SIZE-4)) {
                  printf("Read data error.\n");
                  return -1;
                }
                encoded_len |= ((ch[3] & 0x03) <<11);     //high 2 bit
                encoded_len |= ch[4]<<3;                //middle 8 bit
                encoded_len |= ((ch[5] & 0xe0)>>5);        //low 3bit
                adts_info->profile_aot = ((ch[2]&0xC0)>>6)+1; // get aot // ch【2】最前面的两个 bit
                adts_info->sample_rate = index_to_samplerate((int)(ch[2]&0x3C)>>2);
                channels |= ((ch[2]&0b00000001)<<2); // ch[2]最后一个 bit
                channels |= ((ch[3]&0b11000000)>>6); // ch[3]最前面的两个 bit
                is_adts = 1;
        } else {
                encoded_len = char_to_int(ch);
                is_adts = 0;
        }
        adts_info->encoded_len = encoded_len;
        adts_info->is_adts_format = is_adts;
        adts_info->channels = channels;
        if (encoded_len > buf_size) {
                return -1;
        }
        if (is_adts) {
                memcpy(buffer, ch, ADTS_HEADER_SIZE);
                num_read = fread(buffer+ADTS_HEADER_SIZE, 1, encoded_len-ADTS_HEADER_SIZE, fbitstream);
                if (num_read!=(encoded_len-ADTS_HEADER_SIZE)) {
                  printf("Read data error.\n");
                  return -1;
                }
        } else {
                num_read = fread(buffer, 1, encoded_len, fbitstream);
                if (num_read!=encoded_len) {
                  printf("Read data error.\n");
                  return -1;
                }
        }
        return encoded_len;
}
