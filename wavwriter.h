/* ------------------------------------------------------------------
 * Copyright (C) 2009 <PERSON>
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either
 * express or implied.
 * See the License for the specific language governing permissions
 * and limitations under the License.
 * -------------------------------------------------------------------
 */

#ifndef WAVWRITER_H
#define WAVWRITER_H

#include <stdint.h>
#include <stdio.h>

#ifdef __cplusplus
extern "C" {
#endif

typedef struct {
  int encoded_len;
  int sample_rate;
  int profile_aot;
  int channels;
  int is_adts_format;
} ADTSInfo;

void* wav_write_open(const char *filename, int sample_rate, int bits_per_sample, int channels);
void wav_write_close(void* obj);

void wav_write_data(void* obj, const unsigned char* data, int length);

int getAACFrame(FILE *fbitstream, unsigned char* buffer, int buf_size, ADTSInfo *adts_info);

static void int_to_char(unsigned int i, unsigned char ch[4])
{
  ch[0] = i>>24;
  ch[1] = (i>>16)&0xFF;
  ch[2] = (i>>8)&0xFF;
  ch[3] = i&0xFF;
}

static unsigned int char_to_int(unsigned char ch[4])
{
  return ((unsigned int)ch[0]<<24) | ((unsigned int)ch[1]<<16)
         | ((unsigned int)ch[2]<< 8) |  (unsigned int)ch[3];
}

#ifdef __cplusplus
}
#endif

#endif

