import os
import sys

from scripts.bytesh import JobStatus

import scripts.log as log
import scripts.bytesh as bytesh

default_repo_root_dir = os.path.abspath(os.path.join(os.path.dirname(os.path.abspath(__file__)), '../../../'))
repo_root_dir = os.environ.get('REPO_ROOT_DIR', default_repo_root_dir)

from scripts.submodule_init_repo import submodule_download_decorator


# allow_list=None：下载全部submodule仓库
@submodule_download_decorator(repo_root_dir)
def do(config=None):
    log.info("start run action: {}".format(os.environ['BX_ACTION_NAME']))

    os.chdir(repo_root_dir)

    ret = bytesh.do(['./build_ba_signal_analysis.sh'])
    if ret != 0:
        return JobStatus.failed()
    
    log.print_succ(f'AAC Linux UT success')
    
    return JobStatus.success()